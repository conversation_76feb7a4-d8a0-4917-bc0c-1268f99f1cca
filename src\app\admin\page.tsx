'use client'

import { useTheme } from 'next-themes'
import { useState, useEffect } from 'react'

import {
  AdminHeader,
  Sidebar,
  ProductsSection,
  DebtsSection,
  DashboardStats,
  FamilyGallery,
  APIGraphing,
  History,
  Calendar,
  Settings,
  ProtectedRoute
} from '@/components'
import type { DashboardStats as DashboardStatsType } from '@/types'

export default function AdminPage() {
  const [activeSection, setActiveSection] = useState('dashboard')
  const { resolvedTheme } = useTheme()
  const [stats, setStats] = useState<DashboardStatsType>({
    totalProducts: 0,
    totalDebts: 0,
    totalDebtAmount: 0,
    lowStockItems: 0,
    recentProducts: [],
    recentDebts: []
  })

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      // Fetch products
      const productsRes = await fetch('/api/products')
      const productsData = await productsRes.json()
      const products = productsData.products || []

      // Fetch debts
      const debtsRes = await fetch('/api/debts')
      const debtsData = await debtsRes.json()
      const debts = debtsData.debts || []

      // Calculate stats
      const totalDebtAmount = debts.reduce((sum: number, debt: { total_amount: number }) => sum + debt.total_amount, 0)
      const lowStockProducts = products.filter((product: { stock_quantity: number }) => product.stock_quantity < 10).length

      setStats({
        totalProducts: products.length,
        totalDebts: debts.length,
        totalDebtAmount,
        lowStockItems: lowStockProducts,
        recentProducts: products.slice(0, 5),
        recentDebts: debts.slice(0, 5)
      })
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const renderContent = () => {
    switch (activeSection) {
      case 'products':
        return <ProductsSection onStatsUpdate={fetchStats} />
      case 'debts':
        return <DebtsSection onStatsUpdate={fetchStats} />
      case 'family-gallery':
        return <FamilyGallery />
      case 'api-graphing':
        return <APIGraphing stats={stats} />
      case 'history':
        return <History />
      case 'calendar':
        return <Calendar />
      case 'settings':
        return <Settings />
      default:
        return <DashboardStats stats={stats} />
    }
  }

  const getPageTitle = () => {
    switch (activeSection) {
      case 'dashboard':
        return 'Dashboard'
      case 'products':
        return 'Product Lists'
      case 'debts':
        return 'Customer Debt Management'
      case 'family-gallery':
        return 'Family Gallery'
      case 'api-graphing':
        return 'API Graphing & Visuals'
      case 'history':
        return 'History'
      case 'calendar':
        return 'Calendar'
      case 'settings':
        return 'Settings'
      default:
        return 'Dashboard'
    }
  }

  const getPageDescription = () => {
    switch (activeSection) {
      case 'dashboard':
        return 'Overview of your Revantad Store'
      case 'products':
        return 'Manage your product lists with CRUD operations'
      case 'debts':
        return 'Track customer debt and payments'
      case 'family-gallery':
        return 'Manage family photos and memories'
      case 'api-graphing':
        return 'Visual analytics and business insights'
      case 'history':
        return 'View transaction and activity history'
      case 'calendar':
        return 'Manage events and schedules'
      case 'settings':
        return 'Configure your store settings'
      default:
        return 'Overview of your Revantad Store'
    }
  }

  return (
    <ProtectedRoute>
      <div
        className="min-h-screen bg-gray-50 dark:bg-slate-900 transition-colors duration-300"
        style={{
          backgroundColor: resolvedTheme === 'dark' ? '#0f172a' : '#f9fafb'
        }}
      >
        {/* Facebook-style Header */}
        <AdminHeader
          activeSection={activeSection}
          setActiveSection={setActiveSection}
        />

        <div className="flex pt-16 h-screen">
          {/* Updated Sidebar */}
          <Sidebar activeSection={activeSection} setActiveSection={setActiveSection} />

          {/* Main Content - Fixed height with proper scrollbar constraint */}
          <main
            className="flex-1 transition-colors duration-300 h-[calc(100vh-4rem)] overflow-hidden relative"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#0f172a' : '#ffffff'
            }}
          >
            <div className="h-full overflow-y-auto main-content-scroll">
              <div className="p-4 sm:p-6 lg:p-8">
                <div className="mb-6 sm:mb-8">
                  <h1
                    className="text-2xl sm:text-3xl font-bold transition-colors duration-300"
                    style={{
                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#1f2937'
                    }}
                  >
                    {getPageTitle()}
                  </h1>
                  <p
                    className="mt-2 text-sm sm:text-base transition-colors duration-300"
                    style={{
                      color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                    }}
                  >
                    {getPageDescription()}
                  </p>
                </div>
                {renderContent()}
              </div>
            </div>

            {/* Subtle scroll fade indicators for better UX */}
            <div className="absolute top-0 left-0 right-0 h-4 pointer-events-none z-10 bg-gradient-to-b from-current to-transparent opacity-5"></div>
            <div className="absolute bottom-0 left-0 right-0 h-4 pointer-events-none z-10 bg-gradient-to-t from-current to-transparent opacity-5"></div>
          </main>
        </div>
      </div>
    </ProtectedRoute>
  )
}
