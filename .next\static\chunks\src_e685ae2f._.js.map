{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/AdminHeader.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Search, Home, Package, Users, Image, Moon, Sun, LogOut, User } from 'lucide-react'\nimport Link from 'next/link'\nimport { useTheme } from 'next-themes'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface AdminHeaderProps {\n  activeSection: string\n  setActiveSection: (section: string) => void\n}\n\nexport default function AdminHeader({ activeSection, setActiveSection }: AdminHeaderProps) {\n  const [searchQuery, setSearchQuery] = useState('')\n  const { setTheme, resolvedTheme } = useTheme()\n  const [isProfileOpen, setIsProfileOpen] = useState(false)\n  const [mounted, setMounted] = useState(false)\n  const { user, logout } = useAuth()\n\n  // Handle hydration\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n\n\n  const navigationItems = [\n    {\n      id: 'dashboard',\n      label: 'Home Dashboard',\n      icon: Home,\n      tooltip: 'Dashboard Overview'\n    },\n    {\n      id: 'products',\n      label: 'Product Lists',\n      icon: Package,\n      tooltip: 'Manage Products'\n    },\n    {\n      id: 'debts',\n      label: 'Customer Debts',\n      icon: Users,\n      tooltip: 'Customer Debt Management'\n    },\n    {\n      id: 'family-gallery',\n      label: 'Family Gallery',\n      icon: Image,\n      tooltip: 'Family Photos & Memories'\n    },\n  ]\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault()\n    // Implement search functionality\n    console.log('Searching for:', searchQuery)\n  }\n\n  const toggleTheme = () => {\n    if (!mounted) return\n\n    // Manual DOM manipulation for immediate visual feedback\n    const html = document.documentElement\n    const isDark = resolvedTheme === 'dark'\n\n    if (isDark) {\n      html.classList.remove('dark')\n      setTheme('light')\n    } else {\n      html.classList.add('dark')\n      setTheme('dark')\n    }\n  }\n\n  const handleLogout = () => {\n    logout()\n    window.location.href = '/login'\n  }\n\n  return (\n    <header className=\"fixed top-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 shadow-sm transition-all duration-300\" style={{\n      backgroundColor: resolvedTheme === 'dark' ? '#111827' : '#ffffff',\n      borderColor: resolvedTheme === 'dark' ? '#374151' : '#e5e7eb'\n    }}>\n      <div className=\"grid grid-cols-3 items-center h-16 px-3 sm:px-4 lg:px-6 max-w-full overflow-hidden gap-4\">\n        \n        {/* Left Section - Logo & Search (Fixed Width) */}\n        <div className=\"flex items-center space-x-3 w-auto\">\n          {/* Revantad Logo */}\n          <Link\n            href=\"/landing\"\n            className=\"flex items-center space-x-2 hover:opacity-80 transition-opacity flex-shrink-0\"\n            title=\"Return to Front Page\"\n          >\n            <div className=\"w-10 h-10 hero-gradient rounded-full flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">R</span>\n            </div>\n            <span className=\"text-xl font-bold text-gradient hidden sm:block\">Revantad</span>\n          </Link>\n\n          {/* Search Bar - Much Shorter than Sidebar (320px) */}\n          <form onSubmit={handleSearch} className=\"w-32 sm:w-36 md:w-40 lg:w-44 xl:w-48\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-slate-700 border-0 rounded-full text-sm placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:bg-white dark:focus:bg-slate-600 transition-all duration-200\"\n              />\n            </div>\n          </form>\n        </div>\n\n        {/* Center Section - Navigation Icons (Facebook-style) */}\n        <div className=\"hidden sm:flex items-center justify-center\">\n          <div className=\"flex items-center space-x-3 md:space-x-4 lg:space-x-5\">\n            {navigationItems.map((item) => {\n              const Icon = item.icon\n              const isActive = activeSection === item.id\n\n              return (\n                <button\n                  key={item.id}\n                  onClick={() => setActiveSection(item.id)}\n                  className={`relative p-3 md:p-3.5 lg:p-4 rounded-xl transition-all duration-200 group min-w-[48px] md:min-w-[52px] lg:min-w-[56px] hover:scale-[1.05]`}\n                  style={{\n                    backgroundColor: isActive\n                      ? (resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.2)' : 'rgba(34, 197, 94, 0.1)')\n                      : 'transparent',\n                    color: isActive\n                      ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')\n                      : (resolvedTheme === 'dark' ? '#cbd5e1' : '#374151'),\n                    boxShadow: isActive ? '0 2px 8px rgba(34, 197, 94, 0.2)' : 'none'\n                  }}\n                  title={item.tooltip}\n                  onMouseEnter={(e) => {\n                    if (!isActive) {\n                      e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.5)' : 'rgba(243, 244, 246, 0.8)'\n                      e.currentTarget.style.color = resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'\n                    }\n                  }}\n                  onMouseLeave={(e) => {\n                    if (!isActive) {\n                      e.currentTarget.style.backgroundColor = 'transparent'\n                      e.currentTarget.style.color = resolvedTheme === 'dark' ? '#cbd5e1' : '#374151'\n                    }\n                  }}\n                >\n                  <Icon className=\"h-5 w-5 md:h-6 md:w-6 mx-auto transition-all duration-200\" />\n                  {isActive && (\n                    <div className=\"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-10 md:w-12 lg:w-14 h-1 bg-green-500 rounded-full\"></div>\n                  )}\n\n                  {/* Enhanced Tooltip */}\n                  <div className=\"absolute top-full mt-3 left-1/2 transform -translate-x-1/2 bg-gray-900 dark:bg-slate-700 text-white text-xs px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 whitespace-nowrap pointer-events-none shadow-lg z-10\">\n                    {item.tooltip}\n                    <div className=\"absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-900 dark:bg-slate-700 rotate-45\"></div>\n                  </div>\n                </button>\n              )\n            })}\n          </div>\n        </div>\n\n        {/* Mobile Navigation - Simplified */}\n        <div className=\"sm:hidden flex items-center justify-center\">\n          <button\n            onClick={() => setActiveSection(activeSection === 'dashboard' ? 'products' : 'dashboard')}\n            className=\"p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors\"\n            title=\"Toggle View\"\n          >\n            {activeSection === 'dashboard' ? (\n              <Package className=\"h-5 w-5\" />\n            ) : (\n              <Home className=\"h-5 w-5\" />\n            )}\n          </button>\n        </div>\n\n        {/* Right Section - Dark Mode & Profile */}\n        <div className=\"flex items-center justify-end space-x-3\">\n\n\n          {/* Dark Mode Toggle */}\n          <button\n            onClick={toggleTheme}\n            className=\"p-2.5 rounded-xl text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 hover:text-gray-800 dark:hover:text-gray-200 transition-all duration-200 flex-shrink-0\"\n            title={mounted ? `Switch to ${resolvedTheme === 'dark' ? 'light' : 'dark'} mode (Current: ${resolvedTheme})` : 'Toggle theme'}\n            disabled={!mounted}\n          >\n            {!mounted ? (\n              <div className=\"h-5 w-5 bg-gray-400 rounded-full animate-pulse\" />\n            ) : resolvedTheme === 'dark' ? (\n              <Sun className=\"h-5 w-5\" />\n            ) : (\n              <Moon className=\"h-5 w-5\" />\n            )}\n          </button>\n\n          {/* Profile Dropdown */}\n          <div className=\"relative flex-shrink-0\">\n            <button\n              onClick={() => setIsProfileOpen(!isProfileOpen)}\n              className=\"flex items-center space-x-2 p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-slate-700 transition-all duration-200 group\"\n            >\n              <div className=\"w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow\">\n                <User className=\"h-4 w-4 text-white\" />\n              </div>\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300 hidden sm:block group-hover:text-gray-900 dark:group-hover:text-white transition-colors\">\n                {user?.name || 'Admin'}\n              </span>\n            </button>\n\n            {/* Dropdown Menu */}\n            {isProfileOpen && (\n              <div className=\"absolute right-0 mt-2 w-48 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 py-1\">\n                <div className=\"px-4 py-2 border-b border-gray-200 dark:border-slate-700\">\n                  <p className=\"text-sm font-medium text-gray-900 dark:text-white\">{user?.name || 'Admin User'}</p>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">{user?.email || '<EMAIL>'}</p>\n                </div>\n                \n                <button\n                  onClick={() => setActiveSection('settings')}\n                  className=\"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center space-x-2\"\n                >\n                  <User className=\"h-4 w-4\" />\n                  <span>Settings</span>\n                </button>\n                \n                <button\n                  onClick={handleLogout}\n                  className=\"w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center space-x-2\"\n                >\n                  <LogOut className=\"h-4 w-4\" />\n                  <span>Logout</span>\n                </button>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAae,SAAS,YAAY,EAAE,aAAa,EAAE,gBAAgB,EAAoB;;IACvF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE/B,mBAAmB;IACnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,WAAW;QACb;gCAAG,EAAE;IAIL,MAAM,kBAAkB;QACtB;YACE,IAAI;YACJ,OAAO;YACP,MAAM,sMAAA,CAAA,OAAI;YACV,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,2MAAA,CAAA,UAAO;YACb,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,uMAAA,CAAA,QAAK;YACX,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,uMAAA,CAAA,QAAK;YACX,SAAS;QACX;KACD;IAED,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,iCAAiC;QACjC,QAAQ,GAAG,CAAC,kBAAkB;IAChC;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;QAEd,wDAAwD;QACxD,MAAM,OAAO,SAAS,eAAe;QACrC,MAAM,SAAS,kBAAkB;QAEjC,IAAI,QAAQ;YACV,KAAK,SAAS,CAAC,MAAM,CAAC;YACtB,SAAS;QACX,OAAO;YACL,KAAK,SAAS,CAAC,GAAG,CAAC;YACnB,SAAS;QACX;IACF;IAEA,MAAM,eAAe;QACnB;QACA,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,6LAAC;QAAO,WAAU;QAAgJ,OAAO;YACvK,iBAAiB,kBAAkB,SAAS,YAAY;YACxD,aAAa,kBAAkB,SAAS,YAAY;QACtD;kBACE,cAAA,6LAAC;YAAI,WAAU;;8BAGb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;4BACV,OAAM;;8CAEN,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC;oCAAK,WAAU;8CAAkD;;;;;;;;;;;;sCAIpE,6LAAC;4BAAK,UAAU;4BAAc,WAAU;sCACtC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAOlB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC;4BACpB,MAAM,OAAO,KAAK,IAAI;4BACtB,MAAM,WAAW,kBAAkB,KAAK,EAAE;4BAE1C,qBACE,6LAAC;gCAEC,SAAS,IAAM,iBAAiB,KAAK,EAAE;gCACvC,WAAW,CAAC,yIAAyI,CAAC;gCACtJ,OAAO;oCACL,iBAAiB,WACZ,kBAAkB,SAAS,2BAA2B,2BACvD;oCACJ,OAAO,WACF,kBAAkB,SAAS,YAAY,YACvC,kBAAkB,SAAS,YAAY;oCAC5C,WAAW,WAAW,qCAAqC;gCAC7D;gCACA,OAAO,KAAK,OAAO;gCACnB,cAAc,CAAC;oCACb,IAAI,CAAC,UAAU;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,kBAAkB,SAAS,2BAA2B;wCAC9F,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,kBAAkB,SAAS,YAAY;oCACvE;gCACF;gCACA,cAAc,CAAC;oCACb,IAAI,CAAC,UAAU;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,kBAAkB,SAAS,YAAY;oCACvE;gCACF;;kDAEA,6LAAC;wCAAK,WAAU;;;;;;oCACf,0BACC,6LAAC;wCAAI,WAAU;;;;;;kDAIjB,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,OAAO;0DACb,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;+BAlCZ,KAAK,EAAE;;;;;wBAsClB;;;;;;;;;;;8BAKJ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,SAAS,IAAM,iBAAiB,kBAAkB,cAAc,aAAa;wBAC7E,WAAU;wBACV,OAAM;kCAEL,kBAAkB,4BACjB,6LAAC,2MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;iDAEnB,6LAAC,sMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;;;;;;8BAMtB,6LAAC;oBAAI,WAAU;;sCAIb,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAO,UAAU,CAAC,UAAU,EAAE,kBAAkB,SAAS,UAAU,OAAO,gBAAgB,EAAE,cAAc,CAAC,CAAC,GAAG;4BAC/G,UAAU,CAAC;sCAEV,CAAC,wBACA,6LAAC;gCAAI,WAAU;;;;;uCACb,kBAAkB,uBACpB,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;qDAEf,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAKpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,iBAAiB,CAAC;oCACjC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6LAAC;4CAAK,WAAU;sDACb,MAAM,QAAQ;;;;;;;;;;;;gCAKlB,+BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAqD,MAAM,QAAQ;;;;;;8DAChF,6LAAC;oDAAE,WAAU;8DAA4C,MAAM,SAAS;;;;;;;;;;;;sDAG1E,6LAAC;4CACC,SAAS,IAAM,iBAAiB;4CAChC,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAK;;;;;;;;;;;;sDAGR,6LAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;GA1OwB;;QAEc,mJAAA,CAAA,WAAQ;QAGnB,kIAAA,CAAA,UAAO;;;KALV", "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport {\n  BarChart3,\n  History,\n  Calendar,\n  Settings,\n  ChevronLeft,\n  ChevronRight\n} from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n\ninterface SidebarProps {\n  activeSection: string\n  setActiveSection: (section: string) => void\n}\n\nexport default function Sidebar({ activeSection, setActiveSection }: SidebarProps) {\n  const { resolvedTheme } = useTheme()\n  const [isCollapsed, setIsCollapsed] = useState(false)\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n    const savedState = localStorage.getItem('sidebar-collapsed')\n    if (savedState !== null) {\n      setIsCollapsed(JSON.parse(savedState))\n    }\n  }, [])\n\n  useEffect(() => {\n    if (mounted) {\n      localStorage.setItem('sidebar-collapsed', JSON.stringify(isCollapsed))\n    }\n  }, [isCollapsed, mounted])\n\n  const toggleSidebar = () => {\n    setIsCollapsed(!isCollapsed)\n  }\n\n  const menuItems = [\n    {\n      id: 'api-graphing',\n      label: 'API Graphing & Visuals',\n      icon: BarChart3\n    },\n    {\n      id: 'history',\n      label: 'History',\n      icon: History\n    },\n    {\n      id: 'calendar',\n      label: 'Calendar',\n      icon: Calendar\n    },\n    {\n      id: 'settings',\n      label: 'Settings',\n      icon: Settings\n    },\n    // Additional items to ensure scrollbar visibility\n    {\n      id: 'placeholder-1',\n      label: 'Menu Item 1',\n      icon: BarChart3\n    },\n    {\n      id: 'placeholder-2',\n      label: 'Menu Item 2',\n      icon: History\n    },\n    {\n      id: 'placeholder-3',\n      label: 'Menu Item 3',\n      icon: Calendar\n    },\n    {\n      id: 'placeholder-4',\n      label: 'Menu Item 4',\n      icon: Settings\n    },\n    {\n      id: 'placeholder-5',\n      label: 'Menu Item 5',\n      icon: BarChart3\n    },\n    {\n      id: 'placeholder-6',\n      label: 'Menu Item 6',\n      icon: History\n    },\n  ]\n\n  return (\n    <div\n      className={`shadow-xl border-r sticky top-16 h-[calc(100vh-4rem)] transition-all duration-300 ease-in-out ${\n        isCollapsed ? 'w-20 min-w-20' : 'w-80 min-w-80'\n      }`}\n      style={{\n        backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n        borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb',\n        borderWidth: '1px',\n        boxShadow: resolvedTheme === 'dark'\n          ? '0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.05)'\n          : '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)'\n      }}\n    >\n      <div className=\"flex flex-col h-full\">\n        <div\n          className={`sticky top-0 z-20 transition-all duration-300 backdrop-blur-md ${\n            isCollapsed ? 'px-3 py-3' : 'px-6 py-4'\n          }`}\n          style={{\n            background: resolvedTheme === 'dark'\n              ? 'linear-gradient(135deg, rgba(30, 41, 59, 0.98) 0%, rgba(51, 65, 85, 0.95) 100%)'\n              : 'linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(249, 250, 251, 0.95) 100%)',\n            borderBottom: resolvedTheme === 'dark'\n              ? '1px solid rgba(148, 163, 184, 0.2)'\n              : '1px solid rgba(229, 231, 235, 0.8)',\n            boxShadow: resolvedTheme === 'dark'\n              ? '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)'\n              : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'\n          }}\n        >\n          <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'mb-3'}`}>\n            <div\n              className={`rounded-lg flex items-center justify-center transition-all duration-300 ${\n                isCollapsed ? 'w-10 h-10' : 'w-8 h-8 mr-3'\n              }`}\n              style={{\n                background: resolvedTheme === 'dark'\n                  ? 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)'\n                  : 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n                boxShadow: '0 4px 8px rgba(34, 197, 94, 0.3)'\n              }}\n            >\n              <span className={`text-white font-bold ${isCollapsed ? 'text-base' : 'text-sm'}`}>⚡</span>\n            </div>\n            {!isCollapsed && (\n              <h2\n                className=\"text-lg font-bold transition-all duration-300 crisp-text\"\n                style={{\n                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827',\n                  textShadow: resolvedTheme === 'dark' ? '0 1px 2px rgba(0, 0, 0, 0.3)' : 'none'\n                }}\n              >\n                Additional Tools\n              </h2>\n            )}\n          </div>\n          {!isCollapsed && (\n            <p\n              className=\"text-xs font-medium transition-all duration-300 crisp-text\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b',\n                letterSpacing: '0.025em'\n              }}\n            >\n              Advanced features and utilities\n            </p>\n          )}\n        </div>\n\n        <div className=\"flex-1 relative\">\n          <button\n            onClick={toggleSidebar}\n            className=\"absolute top-4 right-2 z-30 p-2 rounded-lg transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 group\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' \n                ? 'rgba(34, 197, 94, 0.1)' \n                : 'rgba(34, 197, 94, 0.08)',\n              border: resolvedTheme === 'dark' \n                ? '1px solid rgba(34, 197, 94, 0.3)' \n                : '1px solid rgba(34, 197, 94, 0.2)',\n              boxShadow: resolvedTheme === 'dark'\n                ? '0 2px 8px rgba(34, 197, 94, 0.2)'\n                : '0 2px 8px rgba(34, 197, 94, 0.15)'\n            }}\n            title={isCollapsed ? 'Expand Sidebar' : 'Collapse Sidebar'}\n          >\n            {isCollapsed ? (\n              <ChevronRight \n                className=\"w-4 h-4 transition-all duration-200 group-hover:scale-105\" \n                style={{ color: resolvedTheme === 'dark' ? '#4ade80' : '#16a34a' }}\n              />\n            ) : (\n              <ChevronLeft \n                className=\"w-4 h-4 transition-all duration-200 group-hover:scale-105\" \n                style={{ color: resolvedTheme === 'dark' ? '#4ade80' : '#16a34a' }}\n              />\n            )}\n          </button>\n\n          <div className=\"absolute inset-0 scroll-fade-top scroll-fade-bottom\">\n            <nav className={`h-full pt-16 pb-6 overflow-y-auto sidebar-nav-scroll transition-all duration-300 space-y-1 ${\n              isCollapsed ? 'px-2' : 'px-4'\n            }`}>\n        {menuItems.map((item) => {\n          const Icon = item.icon\n          const isActive = activeSection === item.id\n\n          return (\n            <div key={item.id} className=\"relative group\">\n              <button\n                onClick={() => setActiveSection(item.id)}\n                className={`w-full flex items-center text-left transition-all duration-200 group sidebar-nav-item crisp-text relative overflow-hidden ${\n                  isCollapsed\n                    ? 'p-2.5 rounded-lg justify-center'\n                    : 'p-3 rounded-xl'\n                }`}\n                style={{\n                  background: isActive\n                    ? (resolvedTheme === 'dark'\n                      ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.15) 100%)'\n                      : 'linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.08) 100%)')\n                    : 'transparent',\n                  border: isActive\n                    ? (resolvedTheme === 'dark' ? '1px solid rgba(34, 197, 94, 0.4)' : '1px solid rgba(34, 197, 94, 0.3)')\n                    : '1px solid transparent',\n                  boxShadow: isActive\n                    ? (resolvedTheme === 'dark'\n                      ? '0 4px 12px rgba(34, 197, 94, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)'\n                      : '0 4px 12px rgba(34, 197, 94, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.8)')\n                    : 'none'\n                }}\n                onMouseEnter={(e) => {\n                  if (!isActive) {\n                    e.currentTarget.style.background = resolvedTheme === 'dark'\n                      ? 'rgba(71, 85, 105, 0.15)'\n                      : 'rgba(243, 244, 246, 0.6)'\n                    e.currentTarget.style.border = resolvedTheme === 'dark'\n                      ? '1px solid rgba(148, 163, 184, 0.2)'\n                      : '1px solid rgba(229, 231, 235, 0.6)'\n                  }\n                }}\n                onMouseLeave={(e) => {\n                  if (!isActive) {\n                    e.currentTarget.style.background = 'transparent'\n                    e.currentTarget.style.border = '1px solid transparent'\n                  }\n                }}\n              >\n                {isCollapsed ? (\n                  <Icon\n                    className=\"h-5 w-5 transition-all duration-200 relative z-10\"\n                    style={{\n                      color: isActive\n                        ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')\n                        : (resolvedTheme === 'dark' ? '#e2e8f0' : '#64748b'),\n                      filter: isActive ? 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))' : 'none'\n                    }}\n                  />\n                ) : (\n                  <>\n                    <div\n                      className=\"transition-all duration-200 relative p-2 rounded-lg mr-3 overflow-hidden\"\n                      style={{\n                        background: isActive\n                          ? (resolvedTheme === 'dark'\n                            ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.25) 100%)'\n                            : 'linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.15) 100%)')\n                          : (resolvedTheme === 'dark'\n                            ? 'linear-gradient(135deg, rgba(71, 85, 105, 0.5) 0%, rgba(51, 65, 85, 0.4) 100%)'\n                            : 'linear-gradient(135deg, rgba(243, 244, 246, 0.9) 0%, rgba(229, 231, 235, 0.7) 100%)'),\n                        boxShadow: isActive\n                          ? (resolvedTheme === 'dark'\n                            ? '0 2px 8px rgba(34, 197, 94, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)'\n                            : '0 2px 8px rgba(34, 197, 94, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.8)')\n                          : (resolvedTheme === 'dark'\n                            ? 'inset 0 1px 0 rgba(255, 255, 255, 0.05)'\n                            : 'inset 0 1px 0 rgba(255, 255, 255, 0.9)')\n                      }}\n                    >\n                      <Icon\n                        className=\"h-4 w-4 transition-all duration-200 relative z-10\"\n                        style={{\n                          color: isActive\n                            ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')\n                            : (resolvedTheme === 'dark' ? '#e2e8f0' : '#64748b'),\n                          filter: isActive ? 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))' : 'none'\n                        }}\n                      />\n                    </div>\n                    <div className=\"flex-1 sidebar-text\">\n                      <h3\n                        className=\"font-medium text-sm transition-colors duration-200 leading-snug\"\n                        style={{\n                          color: isActive\n                            ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')\n                            : (resolvedTheme === 'dark' ? '#f8fafc' : '#111827'),\n                          fontWeight: isActive ? '600' : '500'\n                        }}\n                      >\n                        {item.label}\n                      </h3>\n                    </div>\n                  </>\n                )}\n              </button>\n              \n              {isCollapsed && (\n                <div\n                  className=\"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 rounded-lg text-sm font-medium opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none z-50 whitespace-nowrap\"\n                  style={{\n                    backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n                    color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827',\n                    border: resolvedTheme === 'dark' ? '1px solid rgba(148, 163, 184, 0.3)' : '1px solid rgba(229, 231, 235, 0.8)',\n                    boxShadow: resolvedTheme === 'dark'\n                      ? '0 4px 12px rgba(0, 0, 0, 0.3)'\n                      : '0 4px 12px rgba(0, 0, 0, 0.15)'\n                  }}\n                >\n                  <div className=\"font-semibold\">{item.label}</div>\n                  <div\n                    className=\"absolute right-full top-1/2 transform -translate-y-1/2 w-0 h-0\"\n                    style={{\n                      borderTop: '6px solid transparent',\n                      borderBottom: '6px solid transparent',\n                      borderRight: `6px solid ${resolvedTheme === 'dark' ? '#1e293b' : '#ffffff'}`\n                    }}\n                  />\n                </div>\n              )}\n            </div>\n          )\n        })}\n            </nav>\n          </div>\n        </div>\n\n        {/* Enhanced Sticky Footer Section */}\n        <div\n          className={`sticky bottom-0 z-20 transition-all duration-300 backdrop-blur-md ${\n            isCollapsed ? 'px-3 py-3' : 'px-6 py-4'\n          }`}\n          style={{\n            background: resolvedTheme === 'dark'\n              ? 'linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.95) 100%)'\n              : 'linear-gradient(135deg, rgba(249, 250, 251, 0.98) 0%, rgba(255, 255, 255, 0.95) 100%)',\n            borderTop: resolvedTheme === 'dark'\n              ? '1px solid rgba(148, 163, 184, 0.2)'\n              : '1px solid rgba(229, 231, 235, 0.8)',\n            boxShadow: resolvedTheme === 'dark'\n              ? '0 -4px 6px -1px rgba(0, 0, 0, 0.3), 0 -2px 4px -1px rgba(0, 0, 0, 0.2)'\n              : '0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 -2px 4px -1px rgba(0, 0, 0, 0.06)'\n          }}\n        >\n          <div\n            className=\"text-sm transition-colors duration-300\"\n            style={{\n              color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b'\n            }}\n          >\n            {isCollapsed ? (\n              <div className=\"flex justify-center\">\n                <div\n                  className=\"rounded-xl flex items-center justify-center relative overflow-hidden w-10 h-10\"\n                  style={{\n                    background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                    boxShadow: '0 4px 8px rgba(59, 130, 246, 0.3)'\n                  }}\n                >\n                  <span className=\"text-white font-bold relative z-10 text-base\">R</span>\n                  <div\n                    className=\"absolute inset-0 opacity-20\"\n                    style={{\n                      background: 'linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%)'\n                    }}\n                  />\n                </div>\n              </div>\n            ) : (\n              <>\n                <div className=\"flex items-center mb-2 space-x-3\">\n                  <div\n                    className=\"rounded-xl flex items-center justify-center relative overflow-hidden w-8 h-8\"\n                    style={{\n                      background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                      boxShadow: '0 4px 8px rgba(59, 130, 246, 0.3)'\n                    }}\n                  >\n                    <span className=\"text-white font-bold relative z-10 text-sm\">R</span>\n                    <div\n                      className=\"absolute inset-0 opacity-20\"\n                      style={{\n                        background: 'linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%)'\n                      }}\n                    />\n                  </div>\n                  <div>\n                    <span\n                      className=\"font-bold text-sm transition-colors duration-300 block\"\n                      style={{\n                        color: resolvedTheme === 'dark' ? '#f8fafc' : '#1e293b',\n                        textShadow: resolvedTheme === 'dark' ? '0 1px 2px rgba(0, 0, 0, 0.3)' : 'none'\n                      }}\n                    >\n                      Revantad Store\n                    </span>\n                    <span\n                      className=\"text-xs font-medium\"\n                      style={{\n                        color: resolvedTheme === 'dark' ? '#64748b' : '#94a3b8',\n                        letterSpacing: '0.025em'\n                      }}\n                    >\n                      Professional Business Management\n                    </span>\n                  </div>\n                </div>\n                <div\n                  className=\"text-xs font-medium px-3 py-2 rounded-lg\"\n                  style={{\n                    backgroundColor: resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.3)' : 'rgba(243, 244, 246, 0.8)',\n                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280',\n                    border: resolvedTheme === 'dark' ? '1px solid rgba(148, 163, 184, 0.2)' : '1px solid rgba(229, 231, 235, 0.6)'\n                  }}\n                >\n                  Admin Dashboard v2.0\n                </div>\n              </>\n            )}\n          </div>\n        </div>\n\n        {/* Scroll Indicator */}\n        {!isCollapsed && (\n          <div\n            className=\"absolute bottom-2 left-1/2 transform -translate-x-1/2 opacity-60 pointer-events-none\"\n            style={{\n              color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b'\n            }}\n          >\n            <div className=\"flex flex-col items-center space-y-1\">\n              <div className=\"text-xs font-medium\">Scroll for more</div>\n              <div className=\"flex space-x-1\">\n                <div className=\"w-1 h-1 rounded-full bg-current animate-pulse\"></div>\n                <div className=\"w-1 h-1 rounded-full bg-current animate-pulse\" style={{ animationDelay: '0.2s' }}></div>\n                <div className=\"w-1 h-1 rounded-full bg-current animate-pulse\" style={{ animationDelay: '0.4s' }}></div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;;;AAXA;;;;AAkBe,SAAS,QAAQ,EAAE,aAAa,EAAE,gBAAgB,EAAgB;;IAC/E,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,WAAW;YACX,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,eAAe,MAAM;gBACvB,eAAe,KAAK,KAAK,CAAC;YAC5B;QACF;4BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,SAAS;gBACX,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;YAC3D;QACF;4BAAG;QAAC;QAAa;KAAQ;IAEzB,MAAM,gBAAgB;QACpB,eAAe,CAAC;IAClB;IAEA,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,OAAO;YACP,MAAM,qNAAA,CAAA,YAAS;QACjB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,2MAAA,CAAA,UAAO;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,6MAAA,CAAA,WAAQ;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,6MAAA,CAAA,WAAQ;QAChB;QACA,kDAAkD;QAClD;YACE,IAAI;YACJ,OAAO;YACP,MAAM,qNAAA,CAAA,YAAS;QACjB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,2MAAA,CAAA,UAAO;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,6MAAA,CAAA,WAAQ;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,6MAAA,CAAA,WAAQ;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,qNAAA,CAAA,YAAS;QACjB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,2MAAA,CAAA,UAAO;QACf;KACD;IAED,qBACE,6LAAC;QACC,WAAW,CAAC,8FAA8F,EACxG,cAAc,kBAAkB,iBAChC;QACF,OAAO;YACL,iBAAiB,kBAAkB,SAAS,YAAY;YACxD,aAAa,kBAAkB,SAAS,YAAY;YACpD,aAAa;YACb,WAAW,kBAAkB,SACzB,8EACA;QACN;kBAEA,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,WAAW,CAAC,+DAA+D,EACzE,cAAc,cAAc,aAC5B;oBACF,OAAO;wBACL,YAAY,kBAAkB,SAC1B,oFACA;wBACJ,cAAc,kBAAkB,SAC5B,uCACA;wBACJ,WAAW,kBAAkB,SACzB,yEACA;oBACN;;sCAEA,6LAAC;4BAAI,WAAW,CAAC,kBAAkB,EAAE,cAAc,mBAAmB,QAAQ;;8CAC5E,6LAAC;oCACC,WAAW,CAAC,wEAAwE,EAClF,cAAc,cAAc,gBAC5B;oCACF,OAAO;wCACL,YAAY,kBAAkB,SAC1B,sDACA;wCACJ,WAAW;oCACb;8CAEA,cAAA,6LAAC;wCAAK,WAAW,CAAC,qBAAqB,EAAE,cAAc,cAAc,WAAW;kDAAE;;;;;;;;;;;gCAEnF,CAAC,6BACA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCACL,OAAO,kBAAkB,SAAS,YAAY;wCAC9C,YAAY,kBAAkB,SAAS,iCAAiC;oCAC1E;8CACD;;;;;;;;;;;;wBAKJ,CAAC,6BACA,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,OAAO,kBAAkB,SAAS,YAAY;gCAC9C,eAAe;4BACjB;sCACD;;;;;;;;;;;;8BAML,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAO;gCACL,iBAAiB,kBAAkB,SAC/B,2BACA;gCACJ,QAAQ,kBAAkB,SACtB,qCACA;gCACJ,WAAW,kBAAkB,SACzB,qCACA;4BACN;4BACA,OAAO,cAAc,mBAAmB;sCAEvC,4BACC,6LAAC,yNAAA,CAAA,eAAY;gCACX,WAAU;gCACV,OAAO;oCAAE,OAAO,kBAAkB,SAAS,YAAY;gCAAU;;;;;qDAGnE,6LAAC,uNAAA,CAAA,cAAW;gCACV,WAAU;gCACV,OAAO;oCAAE,OAAO,kBAAkB,SAAS,YAAY;gCAAU;;;;;;;;;;;sCAKvE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAW,CAAC,2FAA2F,EAC1G,cAAc,SAAS,QACvB;0CACL,UAAU,GAAG,CAAC,CAAC;oCACd,MAAM,OAAO,KAAK,IAAI;oCACtB,MAAM,WAAW,kBAAkB,KAAK,EAAE;oCAE1C,qBACE,6LAAC;wCAAkB,WAAU;;0DAC3B,6LAAC;gDACC,SAAS,IAAM,iBAAiB,KAAK,EAAE;gDACvC,WAAW,CAAC,0HAA0H,EACpI,cACI,oCACA,kBACJ;gDACF,OAAO;oDACL,YAAY,WACP,kBAAkB,SACjB,sFACA,sFACF;oDACJ,QAAQ,WACH,kBAAkB,SAAS,qCAAqC,qCACjE;oDACJ,WAAW,WACN,kBAAkB,SACjB,8EACA,+EACF;gDACN;gDACA,cAAc,CAAC;oDACb,IAAI,CAAC,UAAU;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG,kBAAkB,SACjD,4BACA;wDACJ,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,kBAAkB,SAC7C,uCACA;oDACN;gDACF;gDACA,cAAc,CAAC;oDACb,IAAI,CAAC,UAAU;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;wDACnC,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;oDACjC;gDACF;0DAEC,4BACC,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,OAAO,WACF,kBAAkB,SAAS,YAAY,YACvC,kBAAkB,SAAS,YAAY;wDAC5C,QAAQ,WAAW,8CAA8C;oDACnE;;;;;yEAGF;;sEACE,6LAAC;4DACC,WAAU;4DACV,OAAO;gEACL,YAAY,WACP,kBAAkB,SACjB,sFACA,sFACD,kBAAkB,SACjB,mFACA;gEACN,WAAW,WACN,kBAAkB,SACjB,6EACA,6EACD,kBAAkB,SACjB,4CACA;4DACR;sEAEA,cAAA,6LAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO,WACF,kBAAkB,SAAS,YAAY,YACvC,kBAAkB,SAAS,YAAY;oEAC5C,QAAQ,WAAW,8CAA8C;gEACnE;;;;;;;;;;;sEAGJ,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO,WACF,kBAAkB,SAAS,YAAY,YACvC,kBAAkB,SAAS,YAAY;oEAC5C,YAAY,WAAW,QAAQ;gEACjC;0EAEC,KAAK,KAAK;;;;;;;;;;;;;;;;;;4CAOpB,6BACC,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,iBAAiB,kBAAkB,SAAS,YAAY;oDACxD,OAAO,kBAAkB,SAAS,YAAY;oDAC9C,QAAQ,kBAAkB,SAAS,uCAAuC;oDAC1E,WAAW,kBAAkB,SACzB,kCACA;gDACN;;kEAEA,6LAAC;wDAAI,WAAU;kEAAiB,KAAK,KAAK;;;;;;kEAC1C,6LAAC;wDACC,WAAU;wDACV,OAAO;4DACL,WAAW;4DACX,cAAc;4DACd,aAAa,CAAC,UAAU,EAAE,kBAAkB,SAAS,YAAY,WAAW;wDAC9E;;;;;;;;;;;;;uCArHE,KAAK,EAAE;;;;;gCA2HrB;;;;;;;;;;;;;;;;;8BAMA,6LAAC;oBACC,WAAW,CAAC,kEAAkE,EAC5E,cAAc,cAAc,aAC5B;oBACF,OAAO;wBACL,YAAY,kBAAkB,SAC1B,oFACA;wBACJ,WAAW,kBAAkB,SACzB,uCACA;wBACJ,WAAW,kBAAkB,SACzB,2EACA;oBACN;8BAEA,cAAA,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,OAAO,kBAAkB,SAAS,YAAY;wBAChD;kCAEC,4BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,YAAY;oCACZ,WAAW;gCACb;;kDAEA,6LAAC;wCAAK,WAAU;kDAA+C;;;;;;kDAC/D,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,YAAY;wCACd;;;;;;;;;;;;;;;;iDAKN;;8CACE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,WAAU;4CACV,OAAO;gDACL,YAAY;gDACZ,WAAW;4CACb;;8DAEA,6LAAC;oDAAK,WAAU;8DAA6C;;;;;;8DAC7D,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,YAAY;oDACd;;;;;;;;;;;;sDAGJ,6LAAC;;8DACC,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,OAAO,kBAAkB,SAAS,YAAY;wDAC9C,YAAY,kBAAkB,SAAS,iCAAiC;oDAC1E;8DACD;;;;;;8DAGD,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,OAAO,kBAAkB,SAAS,YAAY;wDAC9C,eAAe;oDACjB;8DACD;;;;;;;;;;;;;;;;;;8CAKL,6LAAC;oCACC,WAAU;oCACV,OAAO;wCACL,iBAAiB,kBAAkB,SAAS,2BAA2B;wCACvE,OAAO,kBAAkB,SAAS,YAAY;wCAC9C,QAAQ,kBAAkB,SAAS,uCAAuC;oCAC5E;8CACD;;;;;;;;;;;;;;;;;;gBASR,CAAC,6BACA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,OAAO,kBAAkB,SAAS,YAAY;oBAChD;8BAEA,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAsB;;;;;;0CACrC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;wCAAgD,OAAO;4CAAE,gBAAgB;wCAAO;;;;;;kDAC/F,6LAAC;wCAAI,WAAU;wCAAgD,OAAO;4CAAE,gBAAgB;wCAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/G;GA9awB;;QACI,mJAAA,CAAA,WAAQ;;;KADZ", "debugId": null}}, {"offset": {"line": 1076, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/DashboardStats.tsx"], "sourcesContent": ["'use client'\n\nimport { Package, Users, DollarSign, AlertTriangle } from 'lucide-react'\nimport { useTheme } from 'next-themes'\n\nimport type { DashboardStats } from '@/types'\n\ninterface DashboardStatsProps {\n  stats: DashboardStats\n}\n\nexport default function DashboardStats({ stats }: DashboardStatsProps) {\n  const { resolvedTheme } = useTheme()\n\n  const statCards = [\n    {\n      title: 'Products in List',\n      value: stats.totalProducts,\n      icon: Package,\n      color: 'bg-blue-500',\n      textColor: 'text-blue-600',\n      bgColor: 'bg-blue-50',\n    },\n    {\n      title: 'Customer Debts',\n      value: stats.totalDebts,\n      icon: Users,\n      color: 'bg-green-500',\n      textColor: 'text-green-600',\n      bgColor: 'bg-green-50',\n    },\n    {\n      title: 'Total Debt Amount',\n      value: `₱${stats.totalDebtAmount.toFixed(2)}`,\n      icon: DollarSign,\n      color: 'bg-yellow-500',\n      textColor: 'text-yellow-600',\n      bgColor: 'bg-yellow-50',\n    },\n    {\n      title: 'Low Stock Items',\n      value: stats.lowStockItems,\n      icon: AlertTriangle,\n      color: 'bg-red-500',\n      textColor: 'text-red-600',\n      bgColor: 'bg-red-50',\n    },\n  ]\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {statCards.map((card, index) => {\n          const Icon = card.icon\n          return (\n            <div\n              key={index}\n              className=\"rounded-lg shadow-md p-6 transition-all duration-300 hover:shadow-lg\"\n              style={{\n                backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n                border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'\n              }}\n            >\n              <div className=\"flex items-center\">\n                <div className={`p-3 rounded-lg ${card.bgColor} ${resolvedTheme === 'dark' ? 'opacity-90' : ''}`}>\n                  <Icon className={`h-6 w-6 ${card.textColor}`} />\n                </div>\n                <div className=\"ml-4\">\n                  <p\n                    className=\"text-sm font-medium transition-colors duration-300\"\n                    style={{\n                      color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                    }}\n                  >\n                    {card.title}\n                  </p>\n                  <p\n                    className=\"text-2xl font-semibold transition-colors duration-300\"\n                    style={{\n                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                    }}\n                  >\n                    {card.value}\n                  </p>\n                </div>\n              </div>\n            </div>\n          )\n        })}\n      </div>\n\n      {/* Quick Actions */}\n      <div\n        className=\"rounded-lg shadow-md p-6 transition-all duration-300\"\n        style={{\n          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n          border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'\n        }}\n      >\n        <h3\n          className=\"text-lg font-semibold mb-4 transition-colors duration-300\"\n          style={{\n            color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n          }}\n        >\n          Quick Actions\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <button\n            className=\"flex items-center p-4 rounded-lg transition-all duration-300 hover:scale-[1.02] hover:shadow-md\"\n            style={{\n              border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #d1d5db',\n              backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb'\n            }}\n            onMouseEnter={(e) => {\n              e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? '#475569' : '#f3f4f6'\n            }}\n            onMouseLeave={(e) => {\n              e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? '#334155' : '#f9fafb'\n            }}\n          >\n            <Package className=\"h-8 w-8 text-blue-600 mr-3\" />\n            <div className=\"text-left\">\n              <p\n                className=\"font-medium transition-colors duration-300\"\n                style={{\n                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                }}\n              >\n                Add to Product List\n              </p>\n              <p\n                className=\"text-sm transition-colors duration-300\"\n                style={{\n                  color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                }}\n              >\n                Add a new product to your list\n              </p>\n            </div>\n          </button>\n          <button\n            className=\"flex items-center p-4 rounded-lg transition-all duration-300 hover:scale-[1.02] hover:shadow-md\"\n            style={{\n              border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #d1d5db',\n              backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb'\n            }}\n            onMouseEnter={(e) => {\n              e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? '#475569' : '#f3f4f6'\n            }}\n            onMouseLeave={(e) => {\n              e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? '#334155' : '#f9fafb'\n            }}\n          >\n            <Users className=\"h-8 w-8 text-green-600 mr-3\" />\n            <div className=\"text-left\">\n              <p\n                className=\"font-medium transition-colors duration-300\"\n                style={{\n                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                }}\n              >\n                Record New Debt\n              </p>\n              <p\n                className=\"text-sm transition-colors duration-300\"\n                style={{\n                  color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                }}\n              >\n                Add a new customer debt record\n              </p>\n            </div>\n          </button>\n        </div>\n      </div>\n\n      {/* Store Overview */}\n      <div\n        className=\"rounded-lg shadow-md p-6 transition-all duration-300\"\n        style={{\n          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n          border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'\n        }}\n      >\n        <h3\n          className=\"text-lg font-semibold mb-4 transition-colors duration-300\"\n          style={{\n            color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n          }}\n        >\n          Store Overview\n        </h3>\n        <div className=\"space-y-4\">\n          <div\n            className=\"flex justify-between items-center py-2 transition-colors duration-300\"\n            style={{\n              borderBottom: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #f3f4f6'\n            }}\n          >\n            <span\n              className=\"transition-colors duration-300\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n              }}\n            >\n              Products in List\n            </span>\n            <span\n              className=\"font-semibold transition-colors duration-300\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n              }}\n            >\n              {stats.totalProducts}\n            </span>\n          </div>\n          <div\n            className=\"flex justify-between items-center py-2 transition-colors duration-300\"\n            style={{\n              borderBottom: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #f3f4f6'\n            }}\n          >\n            <span\n              className=\"transition-colors duration-300\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n              }}\n            >\n              Outstanding Debts\n            </span>\n            <span\n              className=\"font-semibold transition-colors duration-300\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n              }}\n            >\n              {stats.totalDebts}\n            </span>\n          </div>\n          <div\n            className=\"flex justify-between items-center py-2 transition-colors duration-300\"\n            style={{\n              borderBottom: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #f3f4f6'\n            }}\n          >\n            <span\n              className=\"transition-colors duration-300\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n              }}\n            >\n              Total Amount Owed\n            </span>\n            <span\n              className=\"font-semibold transition-colors duration-300\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n              }}\n            >\n              ₱{stats.totalDebtAmount.toFixed(2)}\n            </span>\n          </div>\n          <div className=\"flex justify-between items-center py-2\">\n            <span\n              className=\"transition-colors duration-300\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n              }}\n            >\n              Items Need Restocking\n            </span>\n            <span className={`font-semibold ${stats.lowStockItems > 0 ? 'text-red-600' : 'text-green-600'}`}>\n              {stats.lowStockItems}\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AACA;;;AAHA;;;AAWe,SAAS,eAAe,EAAE,KAAK,EAAuB;;IACnE,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEjC,MAAM,YAAY;QAChB;YACE,OAAO;YACP,OAAO,MAAM,aAAa;YAC1B,MAAM,2MAAA,CAAA,UAAO;YACb,OAAO;YACP,WAAW;YACX,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,MAAM,UAAU;YACvB,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,WAAW;YACX,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,CAAC,CAAC,EAAE,MAAM,eAAe,CAAC,OAAO,CAAC,IAAI;YAC7C,MAAM,qNAAA,CAAA,aAAU;YAChB,OAAO;YACP,WAAW;YACX,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,MAAM,aAAa;YAC1B,MAAM,2NAAA,CAAA,gBAAa;YACnB,OAAO;YACP,WAAW;YACX,SAAS;QACX;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,MAAM;oBACpB,MAAM,OAAO,KAAK,IAAI;oBACtB,qBACE,6LAAC;wBAEC,WAAU;wBACV,OAAO;4BACL,iBAAiB,kBAAkB,SAAS,YAAY;4BACxD,QAAQ,kBAAkB,SAAS,sBAAsB;wBAC3D;kCAEA,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAW,CAAC,eAAe,EAAE,KAAK,OAAO,CAAC,CAAC,EAAE,kBAAkB,SAAS,eAAe,IAAI;8CAC9F,cAAA,6LAAC;wCAAK,WAAW,CAAC,QAAQ,EAAE,KAAK,SAAS,EAAE;;;;;;;;;;;8CAE9C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,WAAU;4CACV,OAAO;gDACL,OAAO,kBAAkB,SAAS,YAAY;4CAChD;sDAEC,KAAK,KAAK;;;;;;sDAEb,6LAAC;4CACC,WAAU;4CACV,OAAO;gDACL,OAAO,kBAAkB,SAAS,YAAY;4CAChD;sDAEC,KAAK,KAAK;;;;;;;;;;;;;;;;;;uBA1BZ;;;;;gBAgCX;;;;;;0BAIF,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,kBAAkB,SAAS,YAAY;oBACxD,QAAQ,kBAAkB,SAAS,sBAAsB;gBAC3D;;kCAEA,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,OAAO,kBAAkB,SAAS,YAAY;wBAChD;kCACD;;;;;;kCAGD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,QAAQ,kBAAkB,SAAS,sBAAsB;oCACzD,iBAAiB,kBAAkB,SAAS,YAAY;gCAC1D;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,kBAAkB,SAAS,YAAY;gCACjF;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,kBAAkB,SAAS,YAAY;gCACjF;;kDAEA,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,OAAO,kBAAkB,SAAS,YAAY;gDAChD;0DACD;;;;;;0DAGD,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,OAAO,kBAAkB,SAAS,YAAY;gDAChD;0DACD;;;;;;;;;;;;;;;;;;0CAKL,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,QAAQ,kBAAkB,SAAS,sBAAsB;oCACzD,iBAAiB,kBAAkB,SAAS,YAAY;gCAC1D;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,kBAAkB,SAAS,YAAY;gCACjF;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,kBAAkB,SAAS,YAAY;gCACjF;;kDAEA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,OAAO,kBAAkB,SAAS,YAAY;gDAChD;0DACD;;;;;;0DAGD,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,OAAO,kBAAkB,SAAS,YAAY;gDAChD;0DACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,kBAAkB,SAAS,YAAY;oBACxD,QAAQ,kBAAkB,SAAS,sBAAsB;gBAC3D;;kCAEA,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,OAAO,kBAAkB,SAAS,YAAY;wBAChD;kCACD;;;;;;kCAGD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,cAAc,kBAAkB,SAAS,sBAAsB;gCACjE;;kDAEA,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;kDACD;;;;;;kDAGD,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;kDAEC,MAAM,aAAa;;;;;;;;;;;;0CAGxB,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,cAAc,kBAAkB,SAAS,sBAAsB;gCACjE;;kDAEA,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;kDACD;;;;;;kDAGD,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;kDAEC,MAAM,UAAU;;;;;;;;;;;;0CAGrB,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,cAAc,kBAAkB,SAAS,sBAAsB;gCACjE;;kDAEA,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;kDACD;;;;;;kDAGD,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;;4CACD;4CACG,MAAM,eAAe,CAAC,OAAO,CAAC;;;;;;;;;;;;;0CAGpC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;kDACD;;;;;;kDAGD,6LAAC;wCAAK,WAAW,CAAC,cAAc,EAAE,MAAM,aAAa,GAAG,IAAI,iBAAiB,kBAAkB;kDAC5F,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC;GA9QwB;;QACI,mJAAA,CAAA,WAAQ;;;KADZ", "debugId": null}}, {"offset": {"line": 1542, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/APIGraphing.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport ReactECharts from 'echarts-for-react'\nimport { TrendingUp, DollarSign, Package, Users, Calendar } from 'lucide-react'\n\nimport type { DashboardStats } from '@/types'\n\ninterface APIGraphingProps {\n  stats: DashboardStats\n}\n\nexport default function APIGraphing({ stats }: APIGraphingProps) {\n  const [salesData, setSalesData] = useState<number[]>([])\n  const [debtData, setDebtData] = useState<number[]>([])\n\n  useEffect(() => {\n    // Simulate API data for charts\n    const generateSalesData = () => {\n      const data = []\n      for (let i = 0; i < 12; i++) {\n        data.push(Math.floor(Math.random() * 50000) + 20000)\n      }\n      setSalesData(data)\n    }\n\n    const generateDebtData = () => {\n      const data = []\n      for (let i = 0; i < 7; i++) {\n        data.push(Math.floor(Math.random() * 15000) + 5000)\n      }\n      setDebtData(data)\n    }\n\n    generateSalesData()\n    generateDebtData()\n  }, [])\n\n  // Monthly Sales Chart\n  const salesChartOption = {\n    title: {\n      text: 'Monthly Sales Revenue',\n      textStyle: {\n        fontSize: 16,\n        fontWeight: 'bold',\n      },\n    },\n    tooltip: {\n      trigger: 'axis',\n      formatter: (params: any) => {\n        return `${params[0].name}<br/>Revenue: ₱${params[0].value.toLocaleString()}`\n      },\n    },\n    xAxis: {\n      type: 'category',\n      data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n    },\n    yAxis: {\n      type: 'value',\n      axisLabel: {\n        formatter: '₱{value}',\n      },\n    },\n    series: [\n      {\n        data: salesData,\n        type: 'line',\n        smooth: true,\n        lineStyle: {\n          color: '#22c55e',\n          width: 3,\n        },\n        itemStyle: {\n          color: '#22c55e',\n        },\n        areaStyle: {\n          color: {\n            type: 'linear',\n            x: 0,\n            y: 0,\n            x2: 0,\n            y2: 1,\n            colorStops: [\n              { offset: 0, color: 'rgba(34, 197, 94, 0.3)' },\n              { offset: 1, color: 'rgba(34, 197, 94, 0.05)' },\n            ],\n          },\n        },\n      },\n    ],\n    grid: {\n      left: '3%',\n      right: '4%',\n      bottom: '3%',\n      containLabel: true,\n    },\n  }\n\n  // Customer Debt Chart\n  const debtChartOption = {\n    title: {\n      text: 'Weekly Customer Debt Trends',\n      textStyle: {\n        fontSize: 16,\n        fontWeight: 'bold',\n      },\n    },\n    tooltip: {\n      trigger: 'axis',\n      formatter: (params: any) => {\n        return `${params[0].name}<br/>Total Debt: ₱${params[0].value.toLocaleString()}`\n      },\n    },\n    xAxis: {\n      type: 'category',\n      data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\n    },\n    yAxis: {\n      type: 'value',\n      axisLabel: {\n        formatter: '₱{value}',\n      },\n    },\n    series: [\n      {\n        data: debtData,\n        type: 'bar',\n        itemStyle: {\n          color: {\n            type: 'linear',\n            x: 0,\n            y: 0,\n            x2: 0,\n            y2: 1,\n            colorStops: [\n              { offset: 0, color: '#facc15' },\n              { offset: 1, color: '#eab308' },\n            ],\n          },\n        },\n        emphasis: {\n          itemStyle: {\n            color: '#f59e0b',\n          },\n        },\n      },\n    ],\n    grid: {\n      left: '3%',\n      right: '4%',\n      bottom: '3%',\n      containLabel: true,\n    },\n  }\n\n  // Product Categories Pie Chart\n  const categoryChartOption = {\n    title: {\n      text: 'Product Categories Distribution',\n      textStyle: {\n        fontSize: 16,\n        fontWeight: 'bold',\n      },\n    },\n    tooltip: {\n      trigger: 'item',\n      formatter: '{a} <br/>{b}: {c} ({d}%)',\n    },\n    series: [\n      {\n        name: 'Categories',\n        type: 'pie',\n        radius: ['40%', '70%'],\n        avoidLabelOverlap: false,\n        itemStyle: {\n          borderRadius: 10,\n          borderColor: '#fff',\n          borderWidth: 2,\n        },\n        label: {\n          show: false,\n          position: 'center',\n        },\n        emphasis: {\n          label: {\n            show: true,\n            fontSize: 20,\n            fontWeight: 'bold',\n          },\n        },\n        labelLine: {\n          show: false,\n        },\n        data: [\n          { value: 35, name: 'Snacks', itemStyle: { color: '#22c55e' } },\n          { value: 25, name: 'Beverages', itemStyle: { color: '#facc15' } },\n          { value: 20, name: 'Canned Goods', itemStyle: { color: '#3b82f6' } },\n          { value: 12, name: 'Personal Care', itemStyle: { color: '#f59e0b' } },\n          { value: 8, name: 'Others', itemStyle: { color: '#8b5cf6' } },\n        ],\n      },\n    ],\n  }\n\n  const kpiCards = [\n    {\n      title: 'Total Revenue',\n      value: '₱' + salesData.reduce((a, b) => a + b, 0).toLocaleString(),\n      icon: DollarSign,\n      color: 'text-green-600',\n      bgColor: 'bg-green-50',\n      change: '+12.5%',\n      changeColor: 'text-green-600',\n    },\n    {\n      title: 'Products Listed',\n      value: stats.totalProducts.toString(),\n      icon: Package,\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-50',\n      change: '+5.2%',\n      changeColor: 'text-blue-600',\n    },\n    {\n      title: 'Active Customers',\n      value: stats.totalDebts.toString(),\n      icon: Users,\n      color: 'text-purple-600',\n      bgColor: 'bg-purple-50',\n      change: '+8.1%',\n      changeColor: 'text-purple-600',\n    },\n    {\n      title: 'Outstanding Debt',\n      value: '₱' + stats.totalDebtAmount.toLocaleString(),\n      icon: TrendingUp,\n      color: 'text-yellow-600',\n      bgColor: 'bg-yellow-50',\n      change: '-3.2%',\n      changeColor: 'text-red-600',\n    },\n  ]\n\n  return (\n    <div className=\"space-y-6\">\n      {/* KPI Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {kpiCards.map((kpi, index) => (\n          <div key={index} className=\"card p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                  {kpi.title}\n                </p>\n                <p className=\"text-2xl font-bold text-gray-900 dark:text-white mt-1\">\n                  {kpi.value}\n                </p>\n                <p className={`text-sm mt-1 ${kpi.changeColor}`}>\n                  {kpi.change} from last month\n                </p>\n              </div>\n              <div className={`p-3 rounded-lg ${kpi.bgColor}`}>\n                <kpi.icon className={`h-6 w-6 ${kpi.color}`} />\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Charts Grid */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Sales Chart */}\n        <div className=\"card p-6\">\n          <ReactECharts option={salesChartOption} style={{ height: '400px' }} />\n        </div>\n\n        {/* Debt Chart */}\n        <div className=\"card p-6\">\n          <ReactECharts option={debtChartOption} style={{ height: '400px' }} />\n        </div>\n      </div>\n\n      {/* Category Distribution */}\n      <div className=\"card p-6\">\n        <ReactECharts option={categoryChartOption} style={{ height: '400px' }} />\n      </div>\n\n      {/* Real-time Data Indicator */}\n      <div className=\"card p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\"></div>\n            <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n              Real-time Data Updates\n            </span>\n          </div>\n          <div className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\n            <Calendar className=\"h-4 w-4\" />\n            <span>Last updated: {new Date().toLocaleTimeString()}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAYe,SAAS,YAAY,EAAE,KAAK,EAAoB;;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,+BAA+B;YAC/B,MAAM;2DAAoB;oBACxB,MAAM,OAAO,EAAE;oBACf,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;wBAC3B,KAAK,IAAI,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS;oBAChD;oBACA,aAAa;gBACf;;YAEA,MAAM;0DAAmB;oBACvB,MAAM,OAAO,EAAE;oBACf,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;wBAC1B,KAAK,IAAI,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS;oBAChD;oBACA,YAAY;gBACd;;YAEA;YACA;QACF;gCAAG,EAAE;IAEL,sBAAsB;IACtB,MAAM,mBAAmB;QACvB,OAAO;YACL,MAAM;YACN,WAAW;gBACT,UAAU;gBACV,YAAY;YACd;QACF;QACA,SAAS;YACP,SAAS;YACT,WAAW,CAAC;gBACV,OAAO,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,cAAc,IAAI;YAC9E;QACF;QACA,OAAO;YACL,MAAM;YACN,MAAM;gBAAC;gBAAO;gBAAO;gBAAO;gBAAO;gBAAO;gBAAO;gBAAO;gBAAO;gBAAO;gBAAO;gBAAO;aAAM;QAC5F;QACA,OAAO;YACL,MAAM;YACN,WAAW;gBACT,WAAW;YACb;QACF;QACA,QAAQ;YACN;gBACE,MAAM;gBACN,MAAM;gBACN,QAAQ;gBACR,WAAW;oBACT,OAAO;oBACP,OAAO;gBACT;gBACA,WAAW;oBACT,OAAO;gBACT;gBACA,WAAW;oBACT,OAAO;wBACL,MAAM;wBACN,GAAG;wBACH,GAAG;wBACH,IAAI;wBACJ,IAAI;wBACJ,YAAY;4BACV;gCAAE,QAAQ;gCAAG,OAAO;4BAAyB;4BAC7C;gCAAE,QAAQ;gCAAG,OAAO;4BAA0B;yBAC/C;oBACH;gBACF;YACF;SACD;QACD,MAAM;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,cAAc;QAChB;IACF;IAEA,sBAAsB;IACtB,MAAM,kBAAkB;QACtB,OAAO;YACL,MAAM;YACN,WAAW;gBACT,UAAU;gBACV,YAAY;YACd;QACF;QACA,SAAS;YACP,SAAS;YACT,WAAW,CAAC;gBACV,OAAO,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,cAAc,IAAI;YACjF;QACF;QACA,OAAO;YACL,MAAM;YACN,MAAM;gBAAC;gBAAO;gBAAO;gBAAO;gBAAO;gBAAO;gBAAO;aAAM;QACzD;QACA,OAAO;YACL,MAAM;YACN,WAAW;gBACT,WAAW;YACb;QACF;QACA,QAAQ;YACN;gBACE,MAAM;gBACN,MAAM;gBACN,WAAW;oBACT,OAAO;wBACL,MAAM;wBACN,GAAG;wBACH,GAAG;wBACH,IAAI;wBACJ,IAAI;wBACJ,YAAY;4BACV;gCAAE,QAAQ;gCAAG,OAAO;4BAAU;4BAC9B;gCAAE,QAAQ;gCAAG,OAAO;4BAAU;yBAC/B;oBACH;gBACF;gBACA,UAAU;oBACR,WAAW;wBACT,OAAO;oBACT;gBACF;YACF;SACD;QACD,MAAM;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,cAAc;QAChB;IACF;IAEA,+BAA+B;IAC/B,MAAM,sBAAsB;QAC1B,OAAO;YACL,MAAM;YACN,WAAW;gBACT,UAAU;gBACV,YAAY;YACd;QACF;QACA,SAAS;YACP,SAAS;YACT,WAAW;QACb;QACA,QAAQ;YACN;gBACE,MAAM;gBACN,MAAM;gBACN,QAAQ;oBAAC;oBAAO;iBAAM;gBACtB,mBAAmB;gBACnB,WAAW;oBACT,cAAc;oBACd,aAAa;oBACb,aAAa;gBACf;gBACA,OAAO;oBACL,MAAM;oBACN,UAAU;gBACZ;gBACA,UAAU;oBACR,OAAO;wBACL,MAAM;wBACN,UAAU;wBACV,YAAY;oBACd;gBACF;gBACA,WAAW;oBACT,MAAM;gBACR;gBACA,MAAM;oBACJ;wBAAE,OAAO;wBAAI,MAAM;wBAAU,WAAW;4BAAE,OAAO;wBAAU;oBAAE;oBAC7D;wBAAE,OAAO;wBAAI,MAAM;wBAAa,WAAW;4BAAE,OAAO;wBAAU;oBAAE;oBAChE;wBAAE,OAAO;wBAAI,MAAM;wBAAgB,WAAW;4BAAE,OAAO;wBAAU;oBAAE;oBACnE;wBAAE,OAAO;wBAAI,MAAM;wBAAiB,WAAW;4BAAE,OAAO;wBAAU;oBAAE;oBACpE;wBAAE,OAAO;wBAAG,MAAM;wBAAU,WAAW;4BAAE,OAAO;wBAAU;oBAAE;iBAC7D;YACH;SACD;IACH;IAEA,MAAM,WAAW;QACf;YACE,OAAO;YACP,OAAO,MAAM,UAAU,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,GAAG,cAAc;YAChE,MAAM,qNAAA,CAAA,aAAU;YAChB,OAAO;YACP,SAAS;YACT,QAAQ;YACR,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO,MAAM,aAAa,CAAC,QAAQ;YACnC,MAAM,2MAAA,CAAA,UAAO;YACb,OAAO;YACP,SAAS;YACT,QAAQ;YACR,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO,MAAM,UAAU,CAAC,QAAQ;YAChC,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;YACT,QAAQ;YACR,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO,MAAM,MAAM,eAAe,CAAC,cAAc;YACjD,MAAM,qNAAA,CAAA,aAAU;YAChB,OAAO;YACP,SAAS;YACT,QAAQ;YACR,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,KAAK,sBAClB,6LAAC;wBAAgB,WAAU;kCACzB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDACV,IAAI,KAAK;;;;;;sDAEZ,6LAAC;4CAAE,WAAU;sDACV,IAAI,KAAK;;;;;;sDAEZ,6LAAC;4CAAE,WAAW,CAAC,aAAa,EAAE,IAAI,WAAW,EAAE;;gDAC5C,IAAI,MAAM;gDAAC;;;;;;;;;;;;;8CAGhB,6LAAC;oCAAI,WAAW,CAAC,eAAe,EAAE,IAAI,OAAO,EAAE;8CAC7C,cAAA,6LAAC,IAAI,IAAI;wCAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,KAAK,EAAE;;;;;;;;;;;;;;;;;uBAdvC;;;;;;;;;;0BAsBd,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,0JAAA,CAAA,UAAY;4BAAC,QAAQ;4BAAkB,OAAO;gCAAE,QAAQ;4BAAQ;;;;;;;;;;;kCAInE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,0JAAA,CAAA,UAAY;4BAAC,QAAQ;4BAAiB,OAAO;gCAAE,QAAQ;4BAAQ;;;;;;;;;;;;;;;;;0BAKpE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,0JAAA,CAAA,UAAY;oBAAC,QAAQ;oBAAqB,OAAO;wBAAE,QAAQ;oBAAQ;;;;;;;;;;;0BAItE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAK,WAAU;8CAAuD;;;;;;;;;;;;sCAIzE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;;wCAAK;wCAAe,IAAI,OAAO,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9D;GApSwB;KAAA", "debugId": null}}, {"offset": {"line": 2076, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/lib/env.ts"], "sourcesContent": ["// Environment variables validation and configuration\n\nimport { z } from 'zod'\n\n// Define the schema for environment variables\nconst envSchema = z.object({\n  // Node environment\n  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),\n  \n  // Supabase configuration\n  NEXT_PUBLIC_SUPABASE_URL: z.string().optional(),\n  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().optional(),\n  SUPABASE_SERVICE_ROLE_KEY: z.string().optional(),\n  \n  // Cloudinary configuration\n  NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME: z.string().optional(),\n  CLOUDINARY_API_KEY: z.string().optional(),\n  CLOUDINARY_API_SECRET: z.string().optional(),\n  \n  // Authentication (optional for build time)\n  NEXTAUTH_SECRET: z.string().optional(),\n  NEXTAUTH_URL: z.string().optional(),\n  \n  // Optional configuration\n  DEBUG: z.string().transform(val => val === 'true').default('false'),\n})\n\n// Parse and validate environment variables\nfunction validateEnv() {\n  try {\n    return envSchema.parse(process.env)\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      const missingVars = error.errors.map(err => `${err.path.join('.')}: ${err.message}`)\n      throw new Error(\n        `❌ Invalid environment variables:\\n${missingVars.join('\\n')}\\n\\n` +\n        `Please check your .env.local file and ensure all required variables are set.\\n` +\n        `See .env.example for reference.`\n      )\n    }\n    throw error\n  }\n}\n\n// Export validated environment variables\nexport const env = validateEnv()\n\n// Environment-specific configurations\nexport const config = {\n  isDevelopment: env.NODE_ENV === 'development',\n  isProduction: env.NODE_ENV === 'production',\n  isTest: env.NODE_ENV === 'test',\n  \n  // Database\n  database: {\n    url: env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co',\n    anonKey: env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key',\n    serviceRoleKey: env.SUPABASE_SERVICE_ROLE_KEY,\n  },\n\n  // File storage\n  cloudinary: {\n    cloudName: env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'placeholder',\n    apiKey: env.CLOUDINARY_API_KEY,\n    apiSecret: env.CLOUDINARY_API_SECRET,\n  },\n  \n  // Authentication\n  auth: {\n    secret: env.NEXTAUTH_SECRET,\n    url: env.NEXTAUTH_URL,\n  },\n  \n  // Debug mode\n  debug: env.DEBUG,\n} as const\n\n// Runtime environment checks\nexport function checkRequiredEnvVars() {\n  const requiredVars = [\n    'NEXT_PUBLIC_SUPABASE_URL',\n    'NEXT_PUBLIC_SUPABASE_ANON_KEY',\n    'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME',\n  ]\n  \n  const missingVars = requiredVars.filter(varName => !process.env[varName])\n  \n  if (missingVars.length > 0) {\n    throw new Error(\n      `❌ Missing required environment variables: ${missingVars.join(', ')}\\n` +\n      `Please check your .env.local file.`\n    )\n  }\n}\n\n// Development-only environment checks\nexport function checkDevelopmentEnvVars() {\n  if (config.isDevelopment) {\n    const devVars = [\n      'CLOUDINARY_API_KEY',\n      'CLOUDINARY_API_SECRET',\n      'NEXTAUTH_SECRET',\n    ]\n    \n    const missingDevVars = devVars.filter(varName => !process.env[varName])\n    \n    if (missingDevVars.length > 0) {\n      console.warn(\n        `⚠️  Missing development environment variables: ${missingDevVars.join(', ')}\\n` +\n        `Some features may not work properly.`\n      )\n    }\n  }\n}\n\n// Production-only environment checks\nexport function checkProductionEnvVars() {\n  if (config.isProduction) {\n    const prodVars = [\n      'SUPABASE_SERVICE_ROLE_KEY',\n      'CLOUDINARY_API_KEY',\n      'CLOUDINARY_API_SECRET',\n      'NEXTAUTH_SECRET',\n      'NEXTAUTH_URL',\n    ]\n    \n    const missingProdVars = prodVars.filter(varName => !process.env[varName])\n    \n    if (missingProdVars.length > 0) {\n      throw new Error(\n        `❌ Missing production environment variables: ${missingProdVars.join(', ')}\\n` +\n        `These are required for production deployment.`\n      )\n    }\n  }\n}\n\n// Initialize environment validation\nexport function initializeEnv() {\n  try {\n    checkRequiredEnvVars()\n    checkDevelopmentEnvVars()\n    checkProductionEnvVars()\n    \n    if (config.debug) {\n      console.log('✅ Environment variables validated successfully')\n      console.log('📊 Configuration:', {\n        environment: env.NODE_ENV,\n        database: !!config.database.url,\n        cloudinary: !!config.cloudinary.cloudName,\n        auth: !!config.auth.secret,\n      })\n    }\n  } catch (error) {\n    console.error(error)\n    if (config.isProduction) {\n      process.exit(1)\n    }\n  }\n}\n\n// Export individual environment variables for convenience\nexport const {\n  NODE_ENV,\n  NEXT_PUBLIC_SUPABASE_URL,\n  NEXT_PUBLIC_SUPABASE_ANON_KEY,\n  SUPABASE_SERVICE_ROLE_KEY,\n  NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,\n  CLOUDINARY_API_KEY,\n  CLOUDINARY_API_SECRET,\n  NEXTAUTH_SECRET,\n  NEXTAUTH_URL,\n  DEBUG,\n} = env\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;;;;;;;;;;;;;;;AA8B1B;AA5B3B;;AAEA,8CAA8C;AAC9C,MAAM,YAAY,qKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzB,mBAAmB;IACnB,UAAU,qKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAe;QAAc;KAAO,EAAE,OAAO,CAAC;IAEhE,yBAAyB;IACzB,0BAA0B,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7C,+BAA+B,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAClD,2BAA2B,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAE9C,2BAA2B;IAC3B,mCAAmC,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACtD,oBAAoB,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACvC,uBAAuB,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAE1C,2CAA2C;IAC3C,iBAAiB,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACpC,cAAc,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAEjC,yBAAyB;IACzB,OAAO,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAA,MAAO,QAAQ,QAAQ,OAAO,CAAC;AAC7D;AAEA,2CAA2C;AAC3C,SAAS;IACP,IAAI;QACF,OAAO,UAAU,KAAK,CAAC,gKAAA,CAAA,UAAO,CAAC,GAAG;IACpC,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,qKAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,MAAM,cAAc,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,OAAO,EAAE;YACnF,MAAM,IAAI,MACR,CAAC,kCAAkC,EAAE,YAAY,IAAI,CAAC,MAAM,IAAI,CAAC,GACjE,CAAC,8EAA8E,CAAC,GAChF,CAAC,+BAA+B,CAAC;QAErC;QACA,MAAM;IACR;AACF;AAGO,MAAM,MAAM;AAGZ,MAAM,SAAS;IACpB,eAAe,IAAI,QAAQ,KAAK;IAChC,cAAc,IAAI,QAAQ,KAAK;IAC/B,QAAQ,IAAI,QAAQ,KAAK;IAEzB,WAAW;IACX,UAAU;QACR,KAAK,IAAI,wBAAwB,IAAI;QACrC,SAAS,IAAI,6BAA6B,IAAI;QAC9C,gBAAgB,IAAI,yBAAyB;IAC/C;IAEA,eAAe;IACf,YAAY;QACV,WAAW,IAAI,iCAAiC,IAAI;QACpD,QAAQ,IAAI,kBAAkB;QAC9B,WAAW,IAAI,qBAAqB;IACtC;IAEA,iBAAiB;IACjB,MAAM;QACJ,QAAQ,IAAI,eAAe;QAC3B,KAAK,IAAI,YAAY;IACvB;IAEA,aAAa;IACb,OAAO,IAAI,KAAK;AAClB;AAGO,SAAS;IACd,MAAM,eAAe;QACnB;QACA;QACA;KACD;IAED,MAAM,cAAc,aAAa,MAAM,CAAC,CAAA,UAAW,CAAC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,QAAQ;IAExE,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,MAAM,IAAI,MACR,CAAC,0CAA0C,EAAE,YAAY,IAAI,CAAC,MAAM,EAAE,CAAC,GACvE,CAAC,kCAAkC,CAAC;IAExC;AACF;AAGO,SAAS;IACd,IAAI,OAAO,aAAa,EAAE;QACxB,MAAM,UAAU;YACd;YACA;YACA;SACD;QAED,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,UAAW,CAAC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,QAAQ;QAEtE,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,QAAQ,IAAI,CACV,CAAC,+CAA+C,EAAE,eAAe,IAAI,CAAC,MAAM,EAAE,CAAC,GAC/E,CAAC,oCAAoC,CAAC;QAE1C;IACF;AACF;AAGO,SAAS;IACd,IAAI,OAAO,YAAY,EAAE;QACvB,MAAM,WAAW;YACf;YACA;YACA;YACA;YACA;SACD;QAED,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAA,UAAW,CAAC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,QAAQ;QAExE,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,MAAM,IAAI,MACR,CAAC,4CAA4C,EAAE,gBAAgB,IAAI,CAAC,MAAM,EAAE,CAAC,GAC7E,CAAC,6CAA6C,CAAC;QAEnD;IACF;AACF;AAGO,SAAS;IACd,IAAI;QACF;QACA;QACA;QAEA,IAAI,OAAO,KAAK,EAAE;YAChB,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,qBAAqB;gBAC/B,aAAa,IAAI,QAAQ;gBACzB,UAAU,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;gBAC/B,YAAY,CAAC,CAAC,OAAO,UAAU,CAAC,SAAS;gBACzC,MAAM,CAAC,CAAC,OAAO,IAAI,CAAC,MAAM;YAC5B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,IAAI,OAAO,YAAY,EAAE;YACvB,gKAAA,CAAA,UAAO,CAAC,IAAI,CAAC;QACf;IACF;AACF;AAGO,MAAM,EACX,QAAQ,EACR,wBAAwB,EACxB,6BAA6B,EAC7B,yBAAyB,EACzB,iCAAiC,EACjC,kBAAkB,EAClB,qBAAqB,EACrB,eAAe,EACf,YAAY,EACZ,KAAK,EACN,GAAG", "debugId": null}}, {"offset": {"line": 2227, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { config } from './env'\n\n// Create Supabase client with validated environment variables\nexport const supabase = createClient(\n  config.database.url,\n  config.database.anonKey,\n  {\n    auth: {\n      autoRefreshToken: true,\n      persistSession: true,\n      detectSessionInUrl: true\n    },\n    db: {\n      schema: 'public'\n    },\n    global: {\n      headers: {\n        'X-Client-Info': 'revantad-store@1.0.0'\n      }\n    }\n  }\n)\n\n// Database Types\nexport interface Product {\n  id: string\n  name: string\n  image_url?: string\n  net_weight: string\n  price: number\n  stock_quantity: number\n  category: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface CustomerDebt {\n  id: string\n  customer_name: string\n  customer_family_name: string\n  product_name: string\n  product_price: number\n  quantity: number\n  total_amount: number\n  debt_date: string\n  created_at: string\n  updated_at: string\n}\n\n// Product Categories\nexport const PRODUCT_CATEGORIES = [\n  'Snacks',\n  'Canned Goods',\n  'Beverages',\n  'Personal Care',\n  'Household Items',\n  'Condiments',\n  'Rice & Grains',\n  'Instant Foods',\n  'Dairy Products',\n  'Others'\n] as const\n\nexport type ProductCategory = typeof PRODUCT_CATEGORIES[number]\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EACjC,oHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,GAAG,EACnB,oHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,OAAO,EACvB;IACE,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;IACtB;IACA,IAAI;QACF,QAAQ;IACV;IACA,QAAQ;QACN,SAAS;YACP,iBAAiB;QACnB;IACF;AACF;AA8BK,MAAM,qBAAqB;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 2271, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/ProductModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { X, Upload, Package } from 'lucide-react'\nimport { Product, PRODUCT_CATEGORIES } from '@/lib/supabase'\n\ninterface ProductModalProps {\n  isOpen: boolean\n  onClose: () => void\n  product?: Product | null\n}\n\nexport default function ProductModal({ isOpen, onClose, product }: ProductModalProps) {\n  const [formData, setFormData] = useState({\n    name: '',\n    net_weight: '',\n    price: '',\n    stock_quantity: '',\n    category: '',\n    image_url: ''\n  })\n  const [imageFile, setImageFile] = useState<File | null>(null)\n  const [imagePreview, setImagePreview] = useState<string>('')\n  const [loading, setLoading] = useState(false)\n  const [uploading, setUploading] = useState(false)\n\n  useEffect(() => {\n    if (product) {\n      setFormData({\n        name: product.name,\n        net_weight: product.net_weight,\n        price: product.price.toString(),\n        stock_quantity: product.stock_quantity.toString(),\n        category: product.category,\n        image_url: product.image_url || ''\n      })\n      setImagePreview(product.image_url || '')\n    } else {\n      setFormData({\n        name: '',\n        net_weight: '',\n        price: '',\n        stock_quantity: '',\n        category: '',\n        image_url: ''\n      })\n      setImagePreview('')\n    }\n    setImageFile(null)\n  }, [product, isOpen])\n\n  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0]\n    if (file) {\n      setImageFile(file)\n      const reader = new FileReader()\n      reader.onloadend = () => {\n        setImagePreview(reader.result as string)\n      }\n      reader.readAsDataURL(file)\n    }\n  }\n\n  const uploadImage = async (): Promise<string> => {\n    if (!imageFile) return formData.image_url\n\n    setUploading(true)\n    try {\n      const uploadFormData = new FormData()\n      uploadFormData.append('file', imageFile)\n\n      const response = await fetch('/api/upload', {\n        method: 'POST',\n        body: uploadFormData,\n      })\n\n      const data = await response.json()\n      return data.url\n    } catch (error) {\n      console.error('Error uploading image:', error)\n      return formData.image_url\n    } finally {\n      setUploading(false)\n    }\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n\n    try {\n      // Upload image if there's a new one\n      const imageUrl = await uploadImage()\n\n      const productData = {\n        ...formData,\n        image_url: imageUrl,\n        price: parseFloat(formData.price),\n        stock_quantity: parseInt(formData.stock_quantity)\n      }\n\n      const url = product ? `/api/products/${product.id}` : '/api/products'\n      const method = product ? 'PUT' : 'POST'\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(productData),\n      })\n\n      if (response.ok) {\n        onClose()\n      } else {\n        console.error('Error saving product')\n      }\n    } catch (error) {\n      console.error('Error saving product:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h2 className=\"text-xl font-semibold\">\n            {product ? 'Edit Product in List' : 'Add Product to List'}\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {/* Image Upload */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Product Image\n            </label>\n            <div className=\"flex flex-col items-center\">\n              <div className=\"w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center mb-2\">\n                {imagePreview ? (\n                  <img\n                    src={imagePreview}\n                    alt=\"Preview\"\n                    className=\"w-full h-full object-cover rounded-lg\"\n                  />\n                ) : (\n                  <Package className=\"h-12 w-12 text-gray-400\" />\n                )}\n              </div>\n              <input\n                type=\"file\"\n                accept=\"image/*\"\n                onChange={handleImageChange}\n                className=\"hidden\"\n                id=\"image-upload\"\n              />\n              <label\n                htmlFor=\"image-upload\"\n                className=\"flex items-center px-3 py-2 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50\"\n              >\n                <Upload className=\"h-4 w-4 mr-2\" />\n                Choose Image\n              </label>\n            </div>\n          </div>\n\n          {/* Product Name */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Product Name *\n            </label>\n            <input\n              type=\"text\"\n              required\n              value={formData.name}\n              onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n\n          {/* Net Weight */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Net Weight *\n            </label>\n            <input\n              type=\"text\"\n              required\n              placeholder=\"e.g., 100g, 1L, 250ml\"\n              value={formData.net_weight}\n              onChange={(e) => setFormData({ ...formData, net_weight: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n\n          {/* Price */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Price (₱) *\n            </label>\n            <input\n              type=\"number\"\n              step=\"0.01\"\n              min=\"0\"\n              required\n              value={formData.price}\n              onChange={(e) => setFormData({ ...formData, price: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n\n          {/* Stock Quantity */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Stock Quantity *\n            </label>\n            <input\n              type=\"number\"\n              min=\"0\"\n              required\n              value={formData.stock_quantity}\n              onChange={(e) => setFormData({ ...formData, stock_quantity: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n\n          {/* Category */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Category *\n            </label>\n            <select\n              required\n              value={formData.category}\n              onChange={(e) => setFormData({ ...formData, category: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"\">Select Category</option>\n              {PRODUCT_CATEGORIES.map(category => (\n                <option key={category} value={category}>{category}</option>\n              ))}\n            </select>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"flex space-x-3 pt-4\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading || uploading}\n              className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\"\n            >\n              {loading || uploading ? 'Saving...' : (product ? 'Update in List' : 'Add to List')}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;;;AAJA;;;;AAYe,SAAS,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAqB;;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,YAAY;QACZ,OAAO;QACP,gBAAgB;QAChB,UAAU;QACV,WAAW;IACb;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,SAAS;gBACX,YAAY;oBACV,MAAM,QAAQ,IAAI;oBAClB,YAAY,QAAQ,UAAU;oBAC9B,OAAO,QAAQ,KAAK,CAAC,QAAQ;oBAC7B,gBAAgB,QAAQ,cAAc,CAAC,QAAQ;oBAC/C,UAAU,QAAQ,QAAQ;oBAC1B,WAAW,QAAQ,SAAS,IAAI;gBAClC;gBACA,gBAAgB,QAAQ,SAAS,IAAI;YACvC,OAAO;gBACL,YAAY;oBACV,MAAM;oBACN,YAAY;oBACZ,OAAO;oBACP,gBAAgB;oBAChB,UAAU;oBACV,WAAW;gBACb;gBACA,gBAAgB;YAClB;YACA,aAAa;QACf;iCAAG;QAAC;QAAS;KAAO;IAEpB,MAAM,oBAAoB,CAAC;QACzB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,MAAM;YACR,aAAa;YACb,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,GAAG;gBACjB,gBAAgB,OAAO,MAAM;YAC/B;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,WAAW,OAAO,SAAS,SAAS;QAEzC,aAAa;QACb,IAAI;YACF,MAAM,iBAAiB,IAAI;YAC3B,eAAe,MAAM,CAAC,QAAQ;YAE9B,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,MAAM;YACR;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO,KAAK,GAAG;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO,SAAS,SAAS;QAC3B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QAEX,IAAI;YACF,oCAAoC;YACpC,MAAM,WAAW,MAAM;YAEvB,MAAM,cAAc;gBAClB,GAAG,QAAQ;gBACX,WAAW;gBACX,OAAO,WAAW,SAAS,KAAK;gBAChC,gBAAgB,SAAS,SAAS,cAAc;YAClD;YAEA,MAAM,MAAM,UAAU,CAAC,cAAc,EAAE,QAAQ,EAAE,EAAE,GAAG;YACtD,MAAM,SAAS,UAAU,QAAQ;YAEjC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,UAAU,yBAAyB;;;;;;sCAEtC,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIjB,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,6BACC,6LAAC;gDACC,KAAK;gDACL,KAAI;gDACJ,WAAU;;;;;qEAGZ,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAGvB,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,UAAU;4CACV,WAAU;4CACV,IAAG;;;;;;sDAEL,6LAAC;4CACC,SAAQ;4CACR,WAAU;;8DAEV,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOzC,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACjE,WAAU;;;;;;;;;;;;sCAKd,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,aAAY;oCACZ,OAAO,SAAS,UAAU;oCAC1B,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACvE,WAAU;;;;;;;;;;;;sCAKd,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,MAAK;oCACL,KAAI;oCACJ,QAAQ;oCACR,OAAO,SAAS,KAAK;oCACrB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAClE,WAAU;;;;;;;;;;;;sCAKd,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAQ;oCACR,OAAO,SAAS,cAAc;oCAC9B,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAC3E,WAAU;;;;;;;;;;;;sCAKd,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,QAAQ;oCACR,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACrE,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,yHAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAA,yBACtB,6LAAC;gDAAsB,OAAO;0DAAW;+CAA5B;;;;;;;;;;;;;;;;;sCAMnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,UAAU,WAAW;oCACrB,WAAU;8CAET,WAAW,YAAY,cAAe,UAAU,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlF;GAvQwB;KAAA", "debugId": null}}, {"offset": {"line": 2740, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/ProductsSection.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Plus, Edit, Trash2, Search, Package } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport ProductModal from './ProductModal'\nimport { Product, PRODUCT_CATEGORIES } from '@/lib/supabase'\n\ninterface ProductsSectionProps {\n  onStatsUpdate: () => void\n}\n\nexport default function ProductsSection({ onStatsUpdate }: ProductsSectionProps) {\n  const { resolvedTheme } = useTheme()\n  const [products, setProducts] = useState<Product[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedCategory, setSelectedCategory] = useState('')\n  const [isModalOpen, setIsModalOpen] = useState(false)\n  const [editingProduct, setEditingProduct] = useState<Product | null>(null)\n\n  useEffect(() => {\n    fetchProducts()\n  }, [])\n\n  const fetchProducts = async () => {\n    try {\n      const response = await fetch('/api/products')\n      const data = await response.json()\n      setProducts(data.products || [])\n    } catch (error) {\n      console.error('Error fetching products:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleDelete = async (id: string) => {\n    if (!confirm('Are you sure you want to delete this product?')) return\n\n    try {\n      const response = await fetch(`/api/products/${id}`, {\n        method: 'DELETE',\n      })\n\n      if (response.ok) {\n        setProducts(products.filter(p => p.id !== id))\n        onStatsUpdate()\n      }\n    } catch (error) {\n      console.error('Error deleting product:', error)\n    }\n  }\n\n  const handleEdit = (product: Product) => {\n    setEditingProduct(product)\n    setIsModalOpen(true)\n  }\n\n  const handleModalClose = () => {\n    setIsModalOpen(false)\n    setEditingProduct(null)\n    fetchProducts()\n    onStatsUpdate()\n  }\n\n  const filteredProducts = products.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase())\n    const matchesCategory = selectedCategory === '' || product.category === selectedCategory\n    return matchesSearch && matchesCategory\n  })\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div className=\"flex space-x-4\">\n          <div className=\"relative\">\n            <Search\n              className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-300\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n              }}\n            />\n            <input\n              type=\"text\"\n              placeholder=\"Search products...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10 pr-4 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300\"\n              style={{\n                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n                border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n                color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n              }}\n            />\n          </div>\n          <select\n            value={selectedCategory}\n            onChange={(e) => setSelectedCategory(e.target.value)}\n            className=\"px-4 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n              border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n              color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n            }}\n          >\n            <option value=\"\">All Categories</option>\n            {PRODUCT_CATEGORIES.map(category => (\n              <option key={category} value={category}>{category}</option>\n            ))}\n          </select>\n        </div>\n        <button\n          onClick={() => setIsModalOpen(true)}\n          className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg\"\n        >\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add to Product List\n        </button>\n      </div>\n\n      {/* Products Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n        {filteredProducts.map((product) => (\n          <div\n            key={product.id}\n            className=\"rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg hover:scale-[1.02]\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n              border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'\n            }}\n          >\n            <div\n              className=\"aspect-square flex items-center justify-center transition-colors duration-300\"\n              style={{\n                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6'\n              }}\n            >\n              {product.image_url ? (\n                <img\n                  src={product.image_url}\n                  alt={product.name}\n                  className=\"w-full h-full object-cover\"\n                />\n              ) : (\n                <Package\n                  className=\"h-16 w-16 transition-colors duration-300\"\n                  style={{\n                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n                  }}\n                />\n              )}\n            </div>\n            <div className=\"p-4\">\n              <h3\n                className=\"font-semibold mb-1 transition-colors duration-300\"\n                style={{\n                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                }}\n              >\n                {product.name}\n              </h3>\n              <p\n                className=\"text-sm mb-2 transition-colors duration-300\"\n                style={{\n                  color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                }}\n              >\n                {product.category}\n              </p>\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-lg font-bold text-green-600\">₱{product.price}</span>\n                <span\n                  className=\"text-sm transition-colors duration-300\"\n                  style={{\n                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n                  }}\n                >\n                  {product.net_weight}\n                </span>\n              </div>\n              <div className=\"flex justify-between items-center mb-4\">\n                <span\n                  className={`text-sm ${product.stock_quantity < 10 ? 'text-red-600' : ''}`}\n                  style={{\n                    color: product.stock_quantity >= 10\n                      ? (resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280')\n                      : '#dc2626'\n                  }}\n                >\n                  Stock: {product.stock_quantity}\n                </span>\n                {product.stock_quantity < 10 && (\n                  <span className=\"text-xs bg-red-100 text-red-800 px-2 py-1 rounded\">Low Stock</span>\n                )}\n              </div>\n              <div className=\"flex space-x-2\">\n                <button\n                  onClick={() => handleEdit(product)}\n                  className=\"flex-1 flex items-center justify-center px-3 py-2 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors\"\n                >\n                  <Edit className=\"h-4 w-4 mr-1\" />\n                  Edit\n                </button>\n                <button\n                  onClick={() => handleDelete(product.id)}\n                  className=\"flex-1 flex items-center justify-center px-3 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors\"\n                >\n                  <Trash2 className=\"h-4 w-4 mr-1\" />\n                  Delete\n                </button>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {filteredProducts.length === 0 && (\n        <div className=\"text-center py-12\">\n          <Package className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No products in list</h3>\n          <p className=\"text-gray-600\">\n            {searchTerm || selectedCategory\n              ? 'Try adjusting your search or filter criteria'\n              : 'Get started by adding your first product to the list'}\n          </p>\n        </div>\n      )}\n\n      {/* Product Modal */}\n      <ProductModal\n        isOpen={isModalOpen}\n        onClose={handleModalClose}\n        product={editingProduct}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAYe,SAAS,gBAAgB,EAAE,aAAa,EAAwB;;IAC7E,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAErE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY,KAAK,QAAQ,IAAI,EAAE;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,kDAAkD;QAE/D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,IAAI,EAAE;gBAClD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC1C;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,kBAAkB;QAClB,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,kBAAkB;QAClB;QACA;IACF;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACvC,MAAM,gBAAgB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAChF,MAAM,kBAAkB,qBAAqB,MAAM,QAAQ,QAAQ,KAAK;QACxE,OAAO,iBAAiB;IAC1B;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCACL,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;;;;;;kDAEF,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;wCACV,OAAO;4CACL,iBAAiB,kBAAkB,SAAS,YAAY;4CACxD,QAAQ,kBAAkB,SAAS,sBAAsB;4CACzD,OAAO,kBAAkB,SAAS,YAAY;wCAChD;;;;;;;;;;;;0CAGJ,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gCACnD,WAAU;gCACV,OAAO;oCACL,iBAAiB,kBAAkB,SAAS,YAAY;oCACxD,QAAQ,kBAAkB,SAAS,sBAAsB;oCACzD,OAAO,kBAAkB,SAAS,YAAY;gCAChD;;kDAEA,6LAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,yHAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAA,yBACtB,6LAAC;4CAAsB,OAAO;sDAAW;2CAA5B;;;;;;;;;;;;;;;;;kCAInB,6LAAC;wBACC,SAAS,IAAM,eAAe;wBAC9B,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;wBAEC,WAAU;wBACV,OAAO;4BACL,iBAAiB,kBAAkB,SAAS,YAAY;4BACxD,QAAQ,kBAAkB,SAAS,sBAAsB;wBAC3D;;0CAEA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,iBAAiB,kBAAkB,SAAS,YAAY;gCAC1D;0CAEC,QAAQ,SAAS,iBAChB,6LAAC;oCACC,KAAK,QAAQ,SAAS;oCACtB,KAAK,QAAQ,IAAI;oCACjB,WAAU;;;;;yDAGZ,6LAAC,2MAAA,CAAA,UAAO;oCACN,WAAU;oCACV,OAAO;wCACL,OAAO,kBAAkB,SAAS,YAAY;oCAChD;;;;;;;;;;;0CAIN,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;kDAEC,QAAQ,IAAI;;;;;;kDAEf,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,OAAO,kBAAkB,SAAS,YAAY;wCAChD;kDAEC,QAAQ,QAAQ;;;;;;kDAEnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;oDAAmC;oDAAE,QAAQ,KAAK;;;;;;;0DAClE,6LAAC;gDACC,WAAU;gDACV,OAAO;oDACL,OAAO,kBAAkB,SAAS,YAAY;gDAChD;0DAEC,QAAQ,UAAU;;;;;;;;;;;;kDAGvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAW,CAAC,QAAQ,EAAE,QAAQ,cAAc,GAAG,KAAK,iBAAiB,IAAI;gDACzE,OAAO;oDACL,OAAO,QAAQ,cAAc,IAAI,KAC5B,kBAAkB,SAAS,YAAY,YACxC;gDACN;;oDACD;oDACS,QAAQ,cAAc;;;;;;;4CAE/B,QAAQ,cAAc,GAAG,oBACxB,6LAAC;gDAAK,WAAU;0DAAoD;;;;;;;;;;;;kDAGxE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,WAAW;gDAC1B,WAAU;;kEAEV,6LAAC,8MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,6LAAC;gDACC,SAAS,IAAM,aAAa,QAAQ,EAAE;gDACtC,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;uBAnFpC,QAAQ,EAAE;;;;;;;;;;YA4FpB,iBAAiB,MAAM,KAAK,mBAC3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,2MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCACV,cAAc,mBACX,iDACA;;;;;;;;;;;;0BAMV,6LAAC,qIAAA,CAAA,UAAY;gBACX,QAAQ;gBACR,SAAS;gBACT,SAAS;;;;;;;;;;;;AAIjB;GA1OwB;;QACI,mJAAA,CAAA,WAAQ;;;KADZ", "debugId": null}}, {"offset": {"line": 3188, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/DebtModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { X } from 'lucide-react'\nimport { CustomerDebt } from '@/lib/supabase'\n\ninterface DebtModalProps {\n  isOpen: boolean\n  onClose: () => void\n  debt?: CustomerDebt | null\n}\n\nexport default function DebtModal({ isOpen, onClose, debt }: DebtModalProps) {\n  const [formData, setFormData] = useState({\n    customer_name: '',\n    customer_family_name: '',\n    product_name: '',\n    product_price: '',\n    quantity: '',\n    debt_date: ''\n  })\n  const [loading, setLoading] = useState(false)\n\n  useEffect(() => {\n    if (debt) {\n      setFormData({\n        customer_name: debt.customer_name,\n        customer_family_name: debt.customer_family_name,\n        product_name: debt.product_name,\n        product_price: debt.product_price.toString(),\n        quantity: debt.quantity.toString(),\n        debt_date: debt.debt_date\n      })\n    } else {\n      setFormData({\n        customer_name: '',\n        customer_family_name: '',\n        product_name: '',\n        product_price: '',\n        quantity: '',\n        debt_date: new Date().toISOString().split('T')[0] || ''\n      })\n    }\n  }, [debt, isOpen])\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n\n    try {\n      const debtData = {\n        ...formData,\n        product_price: parseFloat(formData.product_price),\n        quantity: parseInt(formData.quantity)\n      }\n\n      const url = debt ? `/api/debts/${debt.id}` : '/api/debts'\n      const method = debt ? 'PUT' : 'POST'\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(debtData),\n      })\n\n      if (response.ok) {\n        onClose()\n      } else {\n        console.error('Error saving debt record')\n      }\n    } catch (error) {\n      console.error('Error saving debt record:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (!isOpen) return null\n\n  const totalAmount = formData.product_price && formData.quantity \n    ? (parseFloat(formData.product_price) * parseInt(formData.quantity)).toFixed(2)\n    : '0.00'\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h2 className=\"text-xl font-semibold\">\n            {debt ? 'Edit Debt Record' : 'Add New Debt Record'}\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          {/* Customer Name */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Customer First Name *\n            </label>\n            <input\n              type=\"text\"\n              required\n              value={formData.customer_name}\n              onChange={(e) => setFormData({ ...formData, customer_name: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"e.g., Juan\"\n            />\n          </div>\n\n          {/* Customer Family Name */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Customer Family Name *\n            </label>\n            <input\n              type=\"text\"\n              required\n              value={formData.customer_family_name}\n              onChange={(e) => setFormData({ ...formData, customer_family_name: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"e.g., Dela Cruz\"\n            />\n          </div>\n\n          {/* Product Name */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Product Name *\n            </label>\n            <input\n              type=\"text\"\n              required\n              value={formData.product_name}\n              onChange={(e) => setFormData({ ...formData, product_name: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"e.g., Lucky Me Pancit Canton\"\n            />\n          </div>\n\n          {/* Product Price */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Product Price (₱) *\n            </label>\n            <input\n              type=\"number\"\n              step=\"0.01\"\n              min=\"0\"\n              required\n              value={formData.product_price}\n              onChange={(e) => setFormData({ ...formData, product_price: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"0.00\"\n            />\n          </div>\n\n          {/* Quantity */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Quantity *\n            </label>\n            <input\n              type=\"number\"\n              min=\"1\"\n              required\n              value={formData.quantity}\n              onChange={(e) => setFormData({ ...formData, quantity: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"1\"\n            />\n          </div>\n\n          {/* Debt Date */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Debt Date *\n            </label>\n            <input\n              type=\"date\"\n              required\n              value={formData.debt_date}\n              onChange={(e) => setFormData({ ...formData, debt_date: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n\n          {/* Total Amount Display */}\n          <div className=\"bg-gray-50 p-3 rounded-md\">\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-sm font-medium text-gray-700\">Total Amount:</span>\n              <span className=\"text-lg font-bold text-green-600\">₱{totalAmount}</span>\n            </div>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"flex space-x-3 pt-4\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\"\n            >\n              {loading ? 'Saving...' : (debt ? 'Update' : 'Add Record')}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAYe,SAAS,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAkB;;IACzE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,eAAe;QACf,sBAAsB;QACtB,cAAc;QACd,eAAe;QACf,UAAU;QACV,WAAW;IACb;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,MAAM;gBACR,YAAY;oBACV,eAAe,KAAK,aAAa;oBACjC,sBAAsB,KAAK,oBAAoB;oBAC/C,cAAc,KAAK,YAAY;oBAC/B,eAAe,KAAK,aAAa,CAAC,QAAQ;oBAC1C,UAAU,KAAK,QAAQ,CAAC,QAAQ;oBAChC,WAAW,KAAK,SAAS;gBAC3B;YACF,OAAO;gBACL,YAAY;oBACV,eAAe;oBACf,sBAAsB;oBACtB,cAAc;oBACd,eAAe;oBACf,UAAU;oBACV,WAAW,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;gBACvD;YACF;QACF;8BAAG;QAAC;QAAM;KAAO;IAEjB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QAEX,IAAI;YACF,MAAM,WAAW;gBACf,GAAG,QAAQ;gBACX,eAAe,WAAW,SAAS,aAAa;gBAChD,UAAU,SAAS,SAAS,QAAQ;YACtC;YAEA,MAAM,MAAM,OAAO,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE,GAAG;YAC7C,MAAM,SAAS,OAAO,QAAQ;YAE9B,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,cAAc,SAAS,aAAa,IAAI,SAAS,QAAQ,GAC3D,CAAC,WAAW,SAAS,aAAa,IAAI,SAAS,SAAS,QAAQ,CAAC,EAAE,OAAO,CAAC,KAC3E;IAEJ,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,OAAO,qBAAqB;;;;;;sCAE/B,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIjB,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,aAAa;oCAC7B,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,eAAe,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAC1E,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,oBAAoB;oCACpC,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,sBAAsB,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACjF,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,YAAY;oCAC5B,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,cAAc,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACzE,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,MAAK;oCACL,KAAI;oCACJ,QAAQ;oCACR,OAAO,SAAS,aAAa;oCAC7B,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,eAAe,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAC1E,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAQ;oCACR,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACrE,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,SAAS;oCACzB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACtE,WAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;kDACpD,6LAAC;wCAAK,WAAU;;4CAAmC;4CAAE;;;;;;;;;;;;;;;;;;sCAKzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,UAAU,cAAe,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1D;GAlNwB;KAAA", "debugId": null}}, {"offset": {"line": 3591, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/DebtsSection.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Plus, Edit, Trash2, Search, Users, Calendar } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport DebtModal from './DebtModal'\nimport { CustomerDebt } from '@/lib/supabase'\nimport { format } from 'date-fns'\n\ninterface DebtsSectionProps {\n  onStatsUpdate: () => void\n}\n\nexport default function DebtsSection({ onStatsUpdate }: DebtsSectionProps) {\n  const { resolvedTheme } = useTheme()\n  const [debts, setDebts] = useState<CustomerDebt[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [isModalOpen, setIsModalOpen] = useState(false)\n  const [editingDebt, setEditingDebt] = useState<CustomerDebt | null>(null)\n\n  useEffect(() => {\n    fetchDebts()\n  }, [])\n\n  const fetchDebts = async () => {\n    try {\n      const response = await fetch('/api/debts')\n      const data = await response.json()\n      setDebts(data.debts || [])\n    } catch (error) {\n      console.error('Error fetching debts:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleDelete = async (id: string) => {\n    if (!confirm('Are you sure you want to delete this debt record?')) return\n\n    try {\n      const response = await fetch(`/api/debts/${id}`, {\n        method: 'DELETE',\n      })\n\n      if (response.ok) {\n        setDebts(debts.filter(d => d.id !== id))\n        onStatsUpdate()\n      }\n    } catch (error) {\n      console.error('Error deleting debt:', error)\n    }\n  }\n\n  const handleEdit = (debt: CustomerDebt) => {\n    setEditingDebt(debt)\n    setIsModalOpen(true)\n  }\n\n  const handleModalClose = () => {\n    setIsModalOpen(false)\n    setEditingDebt(null)\n    fetchDebts()\n    onStatsUpdate()\n  }\n\n  const filteredDebts = debts.filter(debt => {\n    const customerName = `${debt.customer_name} ${debt.customer_family_name}`.toLowerCase()\n    const productName = debt.product_name.toLowerCase()\n    const search = searchTerm.toLowerCase()\n    return customerName.includes(search) || productName.includes(search)\n  })\n\n  // Group debts by customer\n  const groupedDebts = filteredDebts.reduce((acc, debt) => {\n    const customerKey = `${debt.customer_name} ${debt.customer_family_name}`\n    if (!acc[customerKey]) {\n      acc[customerKey] = []\n    }\n    acc[customerKey].push(debt)\n    return acc\n  }, {} as Record<string, CustomerDebt[]>)\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div className=\"relative\">\n          <Search\n            className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-300\"\n            style={{\n              color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n            }}\n          />\n          <input\n            type=\"text\"\n            placeholder=\"Search by customer or product...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"pl-10 pr-4 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n              border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n              color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n            }}\n          />\n        </div>\n        <button\n          onClick={() => setIsModalOpen(true)}\n          className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg\"\n        >\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add Debt Record\n        </button>\n      </div>\n\n      {/* Debts List */}\n      <div className=\"space-y-6\">\n        {Object.entries(groupedDebts).map(([customerName, customerDebts]) => {\n          const totalAmount = customerDebts.reduce((sum, debt) => sum + debt.total_amount, 0)\n          \n          return (\n            <div key={customerName} className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n              <div className=\"bg-gray-50 px-6 py-4 border-b border-gray-200\">\n                <div className=\"flex justify-between items-center\">\n                  <div className=\"flex items-center\">\n                    <Users className=\"h-5 w-5 text-gray-400 mr-2\" />\n                    <h3 className=\"text-lg font-semibold text-gray-900\">{customerName}</h3>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-sm text-gray-600\">{customerDebts.length} item(s)</p>\n                    <p className=\"text-lg font-bold text-red-600\">₱{totalAmount.toFixed(2)}</p>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"divide-y divide-gray-200\">\n                {customerDebts.map((debt) => (\n                  <div key={debt.id} className=\"px-6 py-4\">\n                    <div className=\"flex justify-between items-start\">\n                      <div className=\"flex-1\">\n                        <h4 className=\"font-medium text-gray-900\">{debt.product_name}</h4>\n                        <div className=\"mt-1 text-sm text-gray-600 space-y-1\">\n                          <div className=\"flex items-center\">\n                            <span>Quantity: {debt.quantity}</span>\n                            <span className=\"mx-2\">•</span>\n                            <span>Unit Price: ₱{debt.product_price.toFixed(2)}</span>\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Calendar className=\"h-4 w-4 mr-1\" />\n                            <span>Date: {format(new Date(debt.debt_date), 'MMM dd, yyyy')}</span>\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-2 ml-4\">\n                        <div className=\"text-right\">\n                          <p className=\"font-semibold text-gray-900\">₱{debt.total_amount.toFixed(2)}</p>\n                        </div>\n                        <button\n                          onClick={() => handleEdit(debt)}\n                          className=\"p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors\"\n                        >\n                          <Edit className=\"h-4 w-4\" />\n                        </button>\n                        <button\n                          onClick={() => handleDelete(debt.id)}\n                          className=\"p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors\"\n                        >\n                          <Trash2 className=\"h-4 w-4\" />\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )\n        })}\n      </div>\n\n      {filteredDebts.length === 0 && (\n        <div className=\"text-center py-12\">\n          <Users className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No debt records found</h3>\n          <p className=\"text-gray-600\">\n            {searchTerm\n              ? 'Try adjusting your search criteria'\n              : 'Get started by adding your first debt record'}\n          </p>\n        </div>\n      )}\n\n      {/* Debt Modal */}\n      <DebtModal\n        isOpen={isModalOpen}\n        onClose={handleModalClose}\n        debt={editingDebt}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;;;AAPA;;;;;;AAae,SAAS,aAAa,EAAE,aAAa,EAAqB;;IACvE,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAEpE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,SAAS,KAAK,KAAK,IAAI,EAAE;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,sDAAsD;QAEnE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,IAAI,EAAE;gBAC/C,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,SAAS,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACpC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,eAAe;QACf;QACA;IACF;IAEA,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,MAAM,eAAe,GAAG,KAAK,aAAa,CAAC,CAAC,EAAE,KAAK,oBAAoB,EAAE,CAAC,WAAW;QACrF,MAAM,cAAc,KAAK,YAAY,CAAC,WAAW;QACjD,MAAM,SAAS,WAAW,WAAW;QACrC,OAAO,aAAa,QAAQ,CAAC,WAAW,YAAY,QAAQ,CAAC;IAC/D;IAEA,0BAA0B;IAC1B,MAAM,eAAe,cAAc,MAAM,CAAC,CAAC,KAAK;QAC9C,MAAM,cAAc,GAAG,KAAK,aAAa,CAAC,CAAC,EAAE,KAAK,oBAAoB,EAAE;QACxE,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;YACrB,GAAG,CAAC,YAAY,GAAG,EAAE;QACvB;QACA,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC;QACtB,OAAO;IACT,GAAG,CAAC;IAEJ,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCACL,WAAU;gCACV,OAAO;oCACL,OAAO,kBAAkB,SAAS,YAAY;gCAChD;;;;;;0CAEF,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;gCACV,OAAO;oCACL,iBAAiB,kBAAkB,SAAS,YAAY;oCACxD,QAAQ,kBAAkB,SAAS,sBAAsB;oCACzD,OAAO,kBAAkB,SAAS,YAAY;gCAChD;;;;;;;;;;;;kCAGJ,6LAAC;wBACC,SAAS,IAAM,eAAe;wBAC9B,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;0BACZ,OAAO,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,cAAc,cAAc;oBAC9D,MAAM,cAAc,cAAc,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,YAAY,EAAE;oBAEjF,qBACE,6LAAC;wBAAuB,WAAU;;0CAChC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAG,WAAU;8DAAuC;;;;;;;;;;;;sDAEvD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;;wDAAyB,cAAc,MAAM;wDAAC;;;;;;;8DAC3D,6LAAC;oDAAE,WAAU;;wDAAiC;wDAAE,YAAY,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAK1E,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;wCAAkB,WAAU;kDAC3B,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA6B,KAAK,YAAY;;;;;;sEAC5D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;gFAAK;gFAAW,KAAK,QAAQ;;;;;;;sFAC9B,6LAAC;4EAAK,WAAU;sFAAO;;;;;;sFACvB,6LAAC;;gFAAK;gFAAc,KAAK,aAAa,CAAC,OAAO,CAAC;;;;;;;;;;;;;8EAEjD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,6MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;sFACpB,6LAAC;;gFAAK;gFAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;8DAIpD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAU;;oEAA8B;oEAAE,KAAK,YAAY,CAAC,OAAO,CAAC;;;;;;;;;;;;sEAEzE,6LAAC;4DACC,SAAS,IAAM,WAAW;4DAC1B,WAAU;sEAEV,cAAA,6LAAC,8MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6LAAC;4DACC,SAAS,IAAM,aAAa,KAAK,EAAE;4DACnC,WAAU;sEAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCA9BhB,KAAK,EAAE;;;;;;;;;;;uBAhBb;;;;;gBAuDd;;;;;;YAGD,cAAc,MAAM,KAAK,mBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCACV,aACG,uCACA;;;;;;;;;;;;0BAMV,6LAAC,kIAAA,CAAA,UAAS;gBACR,QAAQ;gBACR,SAAS;gBACT,MAAM;;;;;;;;;;;;AAId;GAnMwB;;QACI,mJAAA,CAAA,WAAQ;;;KADZ", "debugId": null}}, {"offset": {"line": 4081, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/FamilyGallery.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Upload, Heart, Share2, Download, Trash2, Plus, Image as ImageIcon } from 'lucide-react'\n\ninterface Photo {\n  id: string\n  url: string\n  title: string\n  description: string\n  date: string\n  likes: number\n  isLiked: boolean\n}\n\nexport default function FamilyGallery() {\n  const [photos, setPhotos] = useState<Photo[]>([\n    {\n      id: '1',\n      url: '/api/placeholder/400/300',\n      title: 'Family Store Opening',\n      description: 'Grand opening of our Revantad Store with the whole family',\n      date: '2024-01-15',\n      likes: 12,\n      isLiked: true,\n    },\n    {\n      id: '2',\n      url: '/api/placeholder/400/300',\n      title: 'Store Anniversary',\n      description: 'Celebrating our first year in business',\n      date: '2024-02-20',\n      likes: 8,\n      isLiked: false,\n    },\n    {\n      id: '3',\n      url: '/api/placeholder/400/300',\n      title: 'Community Event',\n      description: 'Participating in the local community festival',\n      date: '2024-03-10',\n      likes: 15,\n      isLiked: true,\n    },\n  ])\n\n  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false)\n  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null)\n  const [uploadForm, setUploadForm] = useState({\n    title: '',\n    description: '',\n    file: null as File | null,\n  })\n\n  const handleLike = (photoId: string) => {\n    setPhotos(photos.map(photo => \n      photo.id === photoId \n        ? { \n            ...photo, \n            likes: photo.isLiked ? photo.likes - 1 : photo.likes + 1,\n            isLiked: !photo.isLiked \n          }\n        : photo\n    ))\n  }\n\n  const handleDelete = (photoId: string) => {\n    if (confirm('Are you sure you want to delete this photo?')) {\n      setPhotos(photos.filter(photo => photo.id !== photoId))\n    }\n  }\n\n  const handleUpload = (e: React.FormEvent) => {\n    e.preventDefault()\n    if (uploadForm.file && uploadForm.title) {\n      const newPhoto: Photo = {\n        id: Date.now().toString(),\n        url: URL.createObjectURL(uploadForm.file),\n        title: uploadForm.title,\n        description: uploadForm.description,\n        date: new Date().toISOString().split('T')[0] || '',\n        likes: 0,\n        isLiked: false,\n      }\n      setPhotos([newPhoto, ...photos])\n      setUploadForm({ title: '', description: '', file: null })\n      setIsUploadModalOpen(false)\n    }\n  }\n\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0]\n    if (file) {\n      setUploadForm({ ...uploadForm, file })\n    }\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Family Gallery</h2>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            Preserve your family memories and store moments\n          </p>\n        </div>\n        <button\n          onClick={() => setIsUploadModalOpen(true)}\n          className=\"btn-primary flex items-center\"\n        >\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add Photo\n        </button>\n      </div>\n\n      {/* Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"card p-6 text-center\">\n          <ImageIcon className=\"h-8 w-8 text-green-500 mx-auto mb-2\" />\n          <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{photos.length}</p>\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">Total Photos</p>\n        </div>\n        <div className=\"card p-6 text-center\">\n          <Heart className=\"h-8 w-8 text-red-500 mx-auto mb-2\" />\n          <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            {photos.reduce((sum, photo) => sum + photo.likes, 0)}\n          </p>\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">Total Likes</p>\n        </div>\n        <div className=\"card p-6 text-center\">\n          <Upload className=\"h-8 w-8 text-blue-500 mx-auto mb-2\" />\n          <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            {new Date().getFullYear()}\n          </p>\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">Year Started</p>\n        </div>\n      </div>\n\n      {/* Photo Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {photos.map((photo) => (\n          <div key={photo.id} className=\"card overflow-hidden group\">\n            <div className=\"relative aspect-video bg-gray-200 dark:bg-gray-700\">\n              <div className=\"w-full h-full bg-gradient-to-br from-green-100 to-yellow-100 dark:from-green-900 dark:to-yellow-900 flex items-center justify-center\">\n                <ImageIcon className=\"h-16 w-16 text-gray-400\" />\n              </div>\n              <div className=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100\">\n                <button\n                  onClick={() => setSelectedPhoto(photo)}\n                  className=\"bg-white text-gray-900 px-4 py-2 rounded-lg font-medium\"\n                >\n                  View Details\n                </button>\n              </div>\n            </div>\n            \n            <div className=\"p-4\">\n              <h3 className=\"font-semibold text-gray-900 dark:text-white mb-1\">\n                {photo.title}\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2\">\n                {photo.description}\n              </p>\n              \n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  {new Date(photo.date).toLocaleDateString()}\n                </span>\n                \n                <div className=\"flex items-center space-x-2\">\n                  <button\n                    onClick={() => handleLike(photo.id)}\n                    className={`flex items-center space-x-1 text-sm ${\n                      photo.isLiked ? 'text-red-500' : 'text-gray-500 dark:text-gray-400'\n                    }`}\n                  >\n                    <Heart className={`h-4 w-4 ${photo.isLiked ? 'fill-current' : ''}`} />\n                    <span>{photo.likes}</span>\n                  </button>\n                  \n                  <button className=\"text-gray-500 dark:text-gray-400 hover:text-blue-500\">\n                    <Share2 className=\"h-4 w-4\" />\n                  </button>\n                  \n                  <button \n                    onClick={() => handleDelete(photo.id)}\n                    className=\"text-gray-500 dark:text-gray-400 hover:text-red-500\"\n                  >\n                    <Trash2 className=\"h-4 w-4\" />\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Upload Modal */}\n      {isUploadModalOpen && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white dark:bg-slate-800 rounded-lg p-6 w-full max-w-md\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              Add New Photo\n            </h3>\n            \n            <form onSubmit={handleUpload} className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Photo File\n                </label>\n                <input\n                  type=\"file\"\n                  accept=\"image/*\"\n                  onChange={handleFileChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700\"\n                  required\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Title\n                </label>\n                <input\n                  type=\"text\"\n                  value={uploadForm.title}\n                  onChange={(e) => setUploadForm({ ...uploadForm, title: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700\"\n                  required\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Description\n                </label>\n                <textarea\n                  value={uploadForm.description}\n                  onChange={(e) => setUploadForm({ ...uploadForm, description: e.target.value })}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700\"\n                />\n              </div>\n              \n              <div className=\"flex space-x-3 pt-4\">\n                <button\n                  type=\"button\"\n                  onClick={() => setIsUploadModalOpen(false)}\n                  className=\"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-slate-700\"\n                >\n                  Cancel\n                </button>\n                <button\n                  type=\"submit\"\n                  className=\"flex-1 btn-primary\"\n                >\n                  Upload Photo\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n\n      {/* Photo Detail Modal */}\n      {selectedPhoto && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50\">\n          <div className=\"bg-white dark:bg-slate-800 rounded-lg p-6 w-full max-w-2xl\">\n            <div className=\"flex justify-between items-start mb-4\">\n              <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                {selectedPhoto.title}\n              </h3>\n              <button\n                onClick={() => setSelectedPhoto(null)}\n                className=\"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\n              >\n                ✕\n              </button>\n            </div>\n            \n            <div className=\"aspect-video bg-gradient-to-br from-green-100 to-yellow-100 dark:from-green-900 dark:to-yellow-900 rounded-lg mb-4 flex items-center justify-center\">\n              <ImageIcon className=\"h-24 w-24 text-gray-400\" />\n            </div>\n            \n            <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n              {selectedPhoto.description}\n            </p>\n            \n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                {new Date(selectedPhoto.date).toLocaleDateString()}\n              </span>\n              \n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => handleLike(selectedPhoto.id)}\n                  className={`flex items-center space-x-1 ${\n                    selectedPhoto.isLiked ? 'text-red-500' : 'text-gray-500 dark:text-gray-400'\n                  }`}\n                >\n                  <Heart className={`h-5 w-5 ${selectedPhoto.isLiked ? 'fill-current' : ''}`} />\n                  <span>{selectedPhoto.likes}</span>\n                </button>\n                \n                <button className=\"text-gray-500 dark:text-gray-400 hover:text-blue-500\">\n                  <Download className=\"h-5 w-5\" />\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAee,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;QAC5C;YACE,IAAI;YACJ,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;QACX;QACA;YACE,IAAI;YACJ,KAAK;YACL,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;QACX;KACD;IAED,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,OAAO;QACP,aAAa;QACb,MAAM;IACR;IAEA,MAAM,aAAa,CAAC;QAClB,UAAU,OAAO,GAAG,CAAC,CAAA,QACnB,MAAM,EAAE,KAAK,UACT;gBACE,GAAG,KAAK;gBACR,OAAO,MAAM,OAAO,GAAG,MAAM,KAAK,GAAG,IAAI,MAAM,KAAK,GAAG;gBACvD,SAAS,CAAC,MAAM,OAAO;YACzB,IACA;IAER;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,QAAQ,gDAAgD;YAC1D,UAAU,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAChD;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,WAAW,IAAI,IAAI,WAAW,KAAK,EAAE;YACvC,MAAM,WAAkB;gBACtB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,KAAK,IAAI,eAAe,CAAC,WAAW,IAAI;gBACxC,OAAO,WAAW,KAAK;gBACvB,aAAa,WAAW,WAAW;gBACnC,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;gBAChD,OAAO;gBACP,SAAS;YACX;YACA,UAAU;gBAAC;mBAAa;aAAO;YAC/B,cAAc;gBAAE,OAAO;gBAAI,aAAa;gBAAI,MAAM;YAAK;YACvD,qBAAqB;QACvB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,MAAM;YACR,cAAc;gBAAE,GAAG,UAAU;gBAAE;YAAK;QACtC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmD;;;;;;0CACjE,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;kCAIvD,6LAAC;wBACC,SAAS,IAAM,qBAAqB;wBACpC,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAS;gCAAC,WAAU;;;;;;0CACrB,6LAAC;gCAAE,WAAU;0CAAoD,OAAO,MAAM;;;;;;0CAC9E,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAE1D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAE,WAAU;0CACV,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,KAAK,EAAE;;;;;;0CAEpD,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAE1D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCAAE,WAAU;0CACV,IAAI,OAAO,WAAW;;;;;;0CAEzB,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;;;;;;;0BAK5D,6LAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;wBAAmB,WAAU;;0CAC5B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uMAAA,CAAA,QAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,SAAS,IAAM,iBAAiB;4CAChC,WAAU;sDACX;;;;;;;;;;;;;;;;;0CAML,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX,MAAM,KAAK;;;;;;kDAEd,6LAAC;wCAAE,WAAU;kDACV,MAAM,WAAW;;;;;;kDAGpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DACb,IAAI,KAAK,MAAM,IAAI,EAAE,kBAAkB;;;;;;0DAG1C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,WAAW,MAAM,EAAE;wDAClC,WAAW,CAAC,oCAAoC,EAC9C,MAAM,OAAO,GAAG,iBAAiB,oCACjC;;0EAEF,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAW,CAAC,QAAQ,EAAE,MAAM,OAAO,GAAG,iBAAiB,IAAI;;;;;;0EAClE,6LAAC;0EAAM,MAAM,KAAK;;;;;;;;;;;;kEAGpB,6LAAC;wDAAO,WAAU;kEAChB,cAAA,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;kEAGpB,6LAAC;wDACC,SAAS,IAAM,aAAa,MAAM,EAAE;wDACpC,WAAU;kEAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA/ClB,MAAM,EAAE;;;;;;;;;;YAyDrB,mCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2D;;;;;;sCAIzE,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,UAAU;4CACV,WAAU;4CACV,QAAQ;;;;;;;;;;;;8CAIZ,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,OAAO,WAAW,KAAK;4CACvB,UAAU,CAAC,IAAM,cAAc;oDAAE,GAAG,UAAU;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACtE,WAAU;4CACV,QAAQ;;;;;;;;;;;;8CAIZ,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,WAAW,WAAW;4CAC7B,UAAU,CAAC,IAAM,cAAc;oDAAE,GAAG,UAAU;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC5E,MAAM;4CACN,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS,IAAM,qBAAqB;4CACpC,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUV,+BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,cAAc,KAAK;;;;;;8CAEtB,6LAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;;;;;;;sCAKH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,uMAAA,CAAA,QAAS;gCAAC,WAAU;;;;;;;;;;;sCAGvB,6LAAC;4BAAE,WAAU;sCACV,cAAc,WAAW;;;;;;sCAG5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CACb,IAAI,KAAK,cAAc,IAAI,EAAE,kBAAkB;;;;;;8CAGlD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,WAAW,cAAc,EAAE;4CAC1C,WAAW,CAAC,4BAA4B,EACtC,cAAc,OAAO,GAAG,iBAAiB,oCACzC;;8DAEF,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAW,CAAC,QAAQ,EAAE,cAAc,OAAO,GAAG,iBAAiB,IAAI;;;;;;8DAC1E,6LAAC;8DAAM,cAAc,KAAK;;;;;;;;;;;;sDAG5B,6LAAC;4CAAO,WAAU;sDAChB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStC;GA5SwB;KAAA", "debugId": null}}, {"offset": {"line": 4795, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/Calendar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { ChevronLeft, ChevronRight, Plus, Clock, MapPin, Users, Calendar as CalendarIcon } from 'lucide-react'\n\ninterface Event {\n  id: string\n  title: string\n  description: string\n  date: string\n  time: string\n  type: 'delivery' | 'meeting' | 'reminder' | 'holiday' | 'personal'\n  location?: string\n  attendees?: string[]\n}\n\nexport default function Calendar() {\n  const [currentDate, setCurrentDate] = useState(new Date())\n  const [selectedDate, setSelectedDate] = useState<Date | null>(null)\n  const [isEventModalOpen, setIsEventModalOpen] = useState(false)\n  const [events, setEvents] = useState<Event[]>([\n    {\n      id: '1',\n      title: 'Supplier Delivery',\n      description: 'Weekly grocery delivery from main supplier',\n      date: '2024-01-22',\n      time: '09:00',\n      type: 'delivery',\n      location: 'Store Front',\n    },\n    {\n      id: '2',\n      title: 'Monthly Inventory Check',\n      description: 'Complete inventory count and stock verification',\n      date: '2024-01-25',\n      time: '14:00',\n      type: 'reminder',\n    },\n    {\n      id: '3',\n      title: 'Community Meeting',\n      description: 'Barangay business owners meeting',\n      date: '2024-01-28',\n      time: '16:00',\n      type: 'meeting',\n      location: 'Barangay Hall',\n      attendees: ['Maria Santos', 'Juan Dela Cruz', 'Ana Reyes'],\n    },\n    {\n      id: '4',\n      title: 'New Year Holiday',\n      description: 'Store closed for New Year celebration',\n      date: '2024-01-01',\n      time: '00:00',\n      type: 'holiday',\n    },\n  ])\n\n  const [newEvent, setNewEvent] = useState({\n    title: '',\n    description: '',\n    date: '',\n    time: '',\n    type: 'reminder' as Event['type'],\n    location: '',\n  })\n\n  const monthNames = [\n    'January', 'February', 'March', 'April', 'May', 'June',\n    'July', 'August', 'September', 'October', 'November', 'December'\n  ]\n\n  const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']\n\n  const getDaysInMonth = (date: Date) => {\n    const year = date.getFullYear()\n    const month = date.getMonth()\n    const firstDay = new Date(year, month, 1)\n    const lastDay = new Date(year, month + 1, 0)\n    const daysInMonth = lastDay.getDate()\n    const startingDayOfWeek = firstDay.getDay()\n\n    const days = []\n    \n    // Add empty cells for days before the first day of the month\n    for (let i = 0; i < startingDayOfWeek; i++) {\n      days.push(null)\n    }\n    \n    // Add days of the month\n    for (let day = 1; day <= daysInMonth; day++) {\n      days.push(new Date(year, month, day))\n    }\n    \n    return days\n  }\n\n  const getEventsForDate = (date: Date) => {\n    const dateString = date.toISOString().split('T')[0]\n    return events.filter(event => event.date === dateString)\n  }\n\n  const getEventTypeColor = (type: Event['type']) => {\n    switch (type) {\n      case 'delivery':\n        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'\n      case 'meeting':\n        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400'\n      case 'reminder':\n        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'\n      case 'holiday':\n        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'\n      case 'personal':\n        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'\n      default:\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'\n    }\n  }\n\n  const navigateMonth = (direction: 'prev' | 'next') => {\n    setCurrentDate(prev => {\n      const newDate = new Date(prev)\n      if (direction === 'prev') {\n        newDate.setMonth(prev.getMonth() - 1)\n      } else {\n        newDate.setMonth(prev.getMonth() + 1)\n      }\n      return newDate\n    })\n  }\n\n  const handleAddEvent = (e: React.FormEvent) => {\n    e.preventDefault()\n    if (newEvent.title && newEvent.date && newEvent.time) {\n      const event: Event = {\n        id: Date.now().toString(),\n        ...newEvent,\n      }\n      setEvents([...events, event])\n      setNewEvent({\n        title: '',\n        description: '',\n        date: '',\n        time: '',\n        type: 'reminder',\n        location: '',\n      })\n      setIsEventModalOpen(false)\n    }\n  }\n\n  const days = getDaysInMonth(currentDate)\n  const today = new Date()\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Calendar</h2>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            Manage your store events and schedules\n          </p>\n        </div>\n        <button\n          onClick={() => setIsEventModalOpen(true)}\n          className=\"btn-primary flex items-center\"\n        >\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add Event\n        </button>\n      </div>\n\n      {/* Calendar Navigation */}\n      <div className=\"card p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n            {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}\n          </h3>\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={() => navigateMonth('prev')}\n              className=\"p-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-slate-700\"\n            >\n              <ChevronLeft className=\"h-4 w-4\" />\n            </button>\n            <button\n              onClick={() => setCurrentDate(new Date())}\n              className=\"px-4 py-2 text-sm bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/50\"\n            >\n              Today\n            </button>\n            <button\n              onClick={() => navigateMonth('next')}\n              className=\"p-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-slate-700\"\n            >\n              <ChevronRight className=\"h-4 w-4\" />\n            </button>\n          </div>\n        </div>\n\n        {/* Calendar Grid */}\n        <div className=\"grid grid-cols-7 gap-1\">\n          {/* Day headers */}\n          {daysOfWeek.map(day => (\n            <div key={day} className=\"p-3 text-center text-sm font-medium text-gray-500 dark:text-gray-400\">\n              {day}\n            </div>\n          ))}\n          \n          {/* Calendar days */}\n          {days.map((day, index) => {\n            if (!day) {\n              return <div key={index} className=\"p-3 h-24\"></div>\n            }\n            \n            const dayEvents = getEventsForDate(day)\n            const isToday = day.toDateString() === today.toDateString()\n            const isSelected = selectedDate?.toDateString() === day.toDateString()\n            \n            return (\n              <div\n                key={index}\n                onClick={() => setSelectedDate(day)}\n                className={`p-2 h-24 border border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700 ${\n                  isToday ? 'bg-green-50 dark:bg-green-900/20' : ''\n                } ${isSelected ? 'ring-2 ring-green-500' : ''}`}\n              >\n                <div className={`text-sm font-medium mb-1 ${\n                  isToday ? 'text-green-600 dark:text-green-400' : 'text-gray-900 dark:text-white'\n                }`}>\n                  {day.getDate()}\n                </div>\n                \n                <div className=\"space-y-1\">\n                  {dayEvents.slice(0, 2).map(event => (\n                    <div\n                      key={event.id}\n                      className={`text-xs px-1 py-0.5 rounded truncate ${getEventTypeColor(event.type)}`}\n                    >\n                      {event.title}\n                    </div>\n                  ))}\n                  {dayEvents.length > 2 && (\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      +{dayEvents.length - 2} more\n                    </div>\n                  )}\n                </div>\n              </div>\n            )\n          })}\n        </div>\n      </div>\n\n      {/* Upcoming Events */}\n      <div className=\"card p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          Upcoming Events\n        </h3>\n        \n        <div className=\"space-y-3\">\n          {events\n            .filter(event => new Date(event.date) >= today)\n            .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())\n            .slice(0, 5)\n            .map(event => (\n              <div key={event.id} className=\"flex items-start space-x-3 p-3 rounded-lg border border-gray-200 dark:border-gray-700\">\n                <div className={`p-2 rounded-lg ${getEventTypeColor(event.type)}`}>\n                  <CalendarIcon className=\"h-4 w-4\" />\n                </div>\n                \n                <div className=\"flex-1\">\n                  <h4 className=\"font-medium text-gray-900 dark:text-white\">{event.title}</h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">{event.description}</p>\n                  \n                  <div className=\"flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400\">\n                    <div className=\"flex items-center space-x-1\">\n                      <Clock className=\"h-3 w-3\" />\n                      <span>{new Date(event.date).toLocaleDateString()} at {event.time}</span>\n                    </div>\n                    \n                    {event.location && (\n                      <div className=\"flex items-center space-x-1\">\n                        <MapPin className=\"h-3 w-3\" />\n                        <span>{event.location}</span>\n                      </div>\n                    )}\n                    \n                    {event.attendees && (\n                      <div className=\"flex items-center space-x-1\">\n                        <Users className=\"h-3 w-3\" />\n                        <span>{event.attendees.length} attendees</span>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n        </div>\n      </div>\n\n      {/* Add Event Modal */}\n      {isEventModalOpen && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white dark:bg-slate-800 rounded-lg p-6 w-full max-w-md\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              Add New Event\n            </h3>\n            \n            <form onSubmit={handleAddEvent} className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Event Title\n                </label>\n                <input\n                  type=\"text\"\n                  value={newEvent.title}\n                  onChange={(e) => setNewEvent({ ...newEvent, title: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700\"\n                  required\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Description\n                </label>\n                <textarea\n                  value={newEvent.description}\n                  onChange={(e) => setNewEvent({ ...newEvent, description: e.target.value })}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700\"\n                />\n              </div>\n              \n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    Date\n                  </label>\n                  <input\n                    type=\"date\"\n                    value={newEvent.date}\n                    onChange={(e) => setNewEvent({ ...newEvent, date: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700\"\n                    required\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    Time\n                  </label>\n                  <input\n                    type=\"time\"\n                    value={newEvent.time}\n                    onChange={(e) => setNewEvent({ ...newEvent, time: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700\"\n                    required\n                  />\n                </div>\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Event Type\n                </label>\n                <select\n                  value={newEvent.type}\n                  onChange={(e) => setNewEvent({ ...newEvent, type: e.target.value as Event['type'] })}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700\"\n                >\n                  <option value=\"reminder\">Reminder</option>\n                  <option value=\"delivery\">Delivery</option>\n                  <option value=\"meeting\">Meeting</option>\n                  <option value=\"holiday\">Holiday</option>\n                  <option value=\"personal\">Personal</option>\n                </select>\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Location (Optional)\n                </label>\n                <input\n                  type=\"text\"\n                  value={newEvent.location}\n                  onChange={(e) => setNewEvent({ ...newEvent, location: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700\"\n                />\n              </div>\n              \n              <div className=\"flex space-x-3 pt-4\">\n                <button\n                  type=\"button\"\n                  onClick={() => setIsEventModalOpen(false)}\n                  className=\"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-slate-700\"\n                >\n                  Cancel\n                </button>\n                <button\n                  type=\"submit\"\n                  className=\"flex-1 btn-primary\"\n                >\n                  Add Event\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAgBe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;QAC5C;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM;YACN,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YAC<PERSON>,aAAa;YACb,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM;YACN,MAAM;YACN,UAAU;YACV,WAAW;gBAAC;gBAAgB;gBAAkB;aAAY;QAC5D;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM;YACN,MAAM;QACR;KACD;IAED,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;IACZ;IAEA,MAAM,aAAa;QACjB;QAAW;QAAY;QAAS;QAAS;QAAO;QAChD;QAAQ;QAAU;QAAa;QAAW;QAAY;KACvD;IAED,MAAM,aAAa;QAAC;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;KAAM;IAEpE,MAAM,iBAAiB,CAAC;QACtB,MAAM,OAAO,KAAK,WAAW;QAC7B,MAAM,QAAQ,KAAK,QAAQ;QAC3B,MAAM,WAAW,IAAI,KAAK,MAAM,OAAO;QACvC,MAAM,UAAU,IAAI,KAAK,MAAM,QAAQ,GAAG;QAC1C,MAAM,cAAc,QAAQ,OAAO;QACnC,MAAM,oBAAoB,SAAS,MAAM;QAEzC,MAAM,OAAO,EAAE;QAEf,6DAA6D;QAC7D,IAAK,IAAI,IAAI,GAAG,IAAI,mBAAmB,IAAK;YAC1C,KAAK,IAAI,CAAC;QACZ;QAEA,wBAAwB;QACxB,IAAK,IAAI,MAAM,GAAG,OAAO,aAAa,MAAO;YAC3C,KAAK,IAAI,CAAC,IAAI,KAAK,MAAM,OAAO;QAClC;QAEA,OAAO;IACT;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,aAAa,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACnD,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK;IAC/C;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,eAAe,CAAA;YACb,MAAM,UAAU,IAAI,KAAK;YACzB,IAAI,cAAc,QAAQ;gBACxB,QAAQ,QAAQ,CAAC,KAAK,QAAQ,KAAK;YACrC,OAAO;gBACL,QAAQ,QAAQ,CAAC,KAAK,QAAQ,KAAK;YACrC;YACA,OAAO;QACT;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,IAAI,SAAS,KAAK,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,EAAE;YACpD,MAAM,QAAe;gBACnB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,GAAG,QAAQ;YACb;YACA,UAAU;mBAAI;gBAAQ;aAAM;YAC5B,YAAY;gBACV,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,UAAU;YACZ;YACA,oBAAoB;QACtB;IACF;IAEA,MAAM,OAAO,eAAe;IAC5B,MAAM,QAAQ,IAAI;IAElB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmD;;;;;;0CACjE,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;kCAIvD,6LAAC;wBACC,SAAS,IAAM,oBAAoB;wBACnC,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCACX,UAAU,CAAC,YAAY,QAAQ,GAAG;oCAAC;oCAAE,YAAY,WAAW;;;;;;;0CAE/D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAU;kDAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,6LAAC;wCACC,SAAS,IAAM,eAAe,IAAI;wCAClC,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAU;kDAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM9B,6LAAC;wBAAI,WAAU;;4BAEZ,WAAW,GAAG,CAAC,CAAA,oBACd,6LAAC;oCAAc,WAAU;8CACtB;mCADO;;;;;4BAMX,KAAK,GAAG,CAAC,CAAC,KAAK;gCACd,IAAI,CAAC,KAAK;oCACR,qBAAO,6LAAC;wCAAgB,WAAU;uCAAjB;;;;;gCACnB;gCAEA,MAAM,YAAY,iBAAiB;gCACnC,MAAM,UAAU,IAAI,YAAY,OAAO,MAAM,YAAY;gCACzD,MAAM,aAAa,cAAc,mBAAmB,IAAI,YAAY;gCAEpE,qBACE,6LAAC;oCAEC,SAAS,IAAM,gBAAgB;oCAC/B,WAAW,CAAC,6GAA6G,EACvH,UAAU,qCAAqC,GAChD,CAAC,EAAE,aAAa,0BAA0B,IAAI;;sDAE/C,6LAAC;4CAAI,WAAW,CAAC,yBAAyB,EACxC,UAAU,uCAAuC,iCACjD;sDACC,IAAI,OAAO;;;;;;sDAGd,6LAAC;4CAAI,WAAU;;gDACZ,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,sBACzB,6LAAC;wDAEC,WAAW,CAAC,qCAAqC,EAAE,kBAAkB,MAAM,IAAI,GAAG;kEAEjF,MAAM,KAAK;uDAHP,MAAM,EAAE;;;;;gDAMhB,UAAU,MAAM,GAAG,mBAClB,6LAAC;oDAAI,WAAU;;wDAA2C;wDACtD,UAAU,MAAM,GAAG;wDAAE;;;;;;;;;;;;;;mCAvBxB;;;;;4BA6BX;;;;;;;;;;;;;0BAKJ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAIzE,6LAAC;wBAAI,WAAU;kCACZ,OACE,MAAM,CAAC,CAAA,QAAS,IAAI,KAAK,MAAM,IAAI,KAAK,OACxC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,IACpE,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAA,sBACH,6LAAC;gCAAmB,WAAU;;kDAC5B,6LAAC;wCAAI,WAAW,CAAC,eAAe,EAAE,kBAAkB,MAAM,IAAI,GAAG;kDAC/D,cAAA,6LAAC,6MAAA,CAAA,WAAY;4CAAC,WAAU;;;;;;;;;;;kDAG1B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA6C,MAAM,KAAK;;;;;;0DACtE,6LAAC;gDAAE,WAAU;0DAAiD,MAAM,WAAW;;;;;;0DAE/E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;;oEAAM,IAAI,KAAK,MAAM,IAAI,EAAE,kBAAkB;oEAAG;oEAAK,MAAM,IAAI;;;;;;;;;;;;;oDAGjE,MAAM,QAAQ,kBACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC;0EAAM,MAAM,QAAQ;;;;;;;;;;;;oDAIxB,MAAM,SAAS,kBACd,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;;oEAAM,MAAM,SAAS,CAAC,MAAM;oEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;+BAzB9B,MAAM,EAAE;;;;;;;;;;;;;;;;YAoCzB,kCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2D;;;;;;sCAIzE,6LAAC;4BAAK,UAAU;4BAAgB,WAAU;;8CACxC,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAClE,WAAU;4CACV,QAAQ;;;;;;;;;;;;8CAIZ,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,SAAS,WAAW;4CAC3B,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACxE,MAAM;4CACN,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACjE,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,6LAAC;oDACC,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACjE,WAAU;oDACV,QAAQ;;;;;;;;;;;;;;;;;;8CAKd,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,SAAS,IAAI;4CACpB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAkB;4CAClF,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,6LAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAW;;;;;;;;;;;;;;;;;;8CAI7B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACrE,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS,IAAM,oBAAoB;4CACnC,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GA9YwB;KAAA", "debugId": null}}, {"offset": {"line": 5638, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/History.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Search, Download, Clock, User, Package, DollarSign } from 'lucide-react'\n\ninterface HistoryItem {\n  id: string\n  type: 'product' | 'debt' | 'payment' | 'login' | 'system'\n  action: string\n  description: string\n  user: string\n  timestamp: string\n  details?: any\n}\n\nexport default function History() {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [filterType, setFilterType] = useState('all')\n  const [dateRange, setDateRange] = useState('7days')\n\n  const historyData: HistoryItem[] = [\n    {\n      id: '1',\n      type: 'product',\n      action: 'Product Added',\n      description: 'Added \"Lucky Me Pancit Canton\" to product list',\n      user: 'Admin',\n      timestamp: '2024-01-20T10:30:00Z',\n      details: { productName: 'Lucky Me Pancit Canton', price: 15.00 }\n    },\n    {\n      id: '2',\n      type: 'debt',\n      action: 'Debt Recorded',\n      description: 'New debt record for Juan Dela Cruz',\n      user: 'Admin',\n      timestamp: '2024-01-20T09:15:00Z',\n      details: { customer: 'Juan <PERSON>', amount: 45.00 }\n    },\n    {\n      id: '3',\n      type: 'payment',\n      action: 'Payment Received',\n      description: 'Payment received from <PERSON>',\n      user: 'Admin',\n      timestamp: '2024-01-19T16:45:00Z',\n      details: { customer: 'Maria Santos', amount: 120.00 }\n    },\n    {\n      id: '4',\n      type: 'product',\n      action: 'Stock Updated',\n      description: 'Updated stock quantity for Coca-Cola',\n      user: 'Admin',\n      timestamp: '2024-01-19T14:20:00Z',\n      details: { productName: 'Coca-Cola', oldStock: 25, newStock: 50 }\n    },\n    {\n      id: '5',\n      type: 'login',\n      action: 'User Login',\n      description: 'Admin user logged into the system',\n      user: 'Admin',\n      timestamp: '2024-01-19T08:00:00Z',\n      details: { ipAddress: '*************' }\n    },\n    {\n      id: '6',\n      type: 'system',\n      action: 'Backup Created',\n      description: 'Automatic database backup completed',\n      user: 'System',\n      timestamp: '2024-01-19T02:00:00Z',\n      details: { backupSize: '2.5MB' }\n    },\n    {\n      id: '7',\n      type: 'debt',\n      action: 'Debt Updated',\n      description: 'Updated debt record for Ana Reyes',\n      user: 'Admin',\n      timestamp: '2024-01-18T15:30:00Z',\n      details: { customer: 'Ana Reyes', oldAmount: 75.00, newAmount: 100.00 }\n    },\n    {\n      id: '8',\n      type: 'product',\n      action: 'Product Deleted',\n      description: 'Removed \"Expired Milk\" from product list',\n      user: 'Admin',\n      timestamp: '2024-01-18T11:10:00Z',\n      details: { productName: 'Expired Milk' }\n    },\n  ]\n\n  const getTypeIcon = (type: string) => {\n    switch (type) {\n      case 'product':\n        return <Package className=\"h-4 w-4\" />\n      case 'debt':\n      case 'payment':\n        return <DollarSign className=\"h-4 w-4\" />\n      case 'login':\n        return <User className=\"h-4 w-4\" />\n      case 'system':\n        return <Clock className=\"h-4 w-4\" />\n      default:\n        return <Clock className=\"h-4 w-4\" />\n    }\n  }\n\n  const getTypeColor = (type: string) => {\n    switch (type) {\n      case 'product':\n        return 'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400'\n      case 'debt':\n        return 'bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400'\n      case 'payment':\n        return 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400'\n      case 'login':\n        return 'bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400'\n      case 'system':\n        return 'bg-gray-100 text-gray-600 dark:bg-gray-900/30 dark:text-gray-400'\n      default:\n        return 'bg-gray-100 text-gray-600 dark:bg-gray-900/30 dark:text-gray-400'\n    }\n  }\n\n  const filteredHistory = historyData.filter(item => {\n    const matchesSearch = item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         item.action.toLowerCase().includes(searchTerm.toLowerCase())\n    const matchesType = filterType === 'all' || item.type === filterType\n    return matchesSearch && matchesType\n  })\n\n  const formatTimestamp = (timestamp: string) => {\n    const date = new Date(timestamp)\n    const now = new Date()\n    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))\n    \n    if (diffInHours < 1) {\n      return 'Just now'\n    } else if (diffInHours < 24) {\n      return `${diffInHours} hours ago`\n    } else {\n      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()\n    }\n  }\n\n  const exportHistory = () => {\n    const csvContent = [\n      ['Timestamp', 'Type', 'Action', 'Description', 'User'],\n      ...filteredHistory.map(item => [\n        item.timestamp,\n        item.type,\n        item.action,\n        item.description,\n        item.user\n      ])\n    ].map(row => row.join(',')).join('\\n')\n\n    const blob = new Blob([csvContent], { type: 'text/csv' })\n    const url = window.URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = url\n    a.download = `revantad-store-history-${new Date().toISOString().split('T')[0]}.csv`\n    a.click()\n    window.URL.revokeObjectURL(url)\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Activity History</h2>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            Track all activities and changes in your store\n          </p>\n        </div>\n        <button\n          onClick={exportHistory}\n          className=\"btn-primary flex items-center\"\n        >\n          <Download className=\"h-4 w-4 mr-2\" />\n          Export History\n        </button>\n      </div>\n\n      {/* Filters */}\n      <div className=\"card p-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          {/* Search */}\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search activities...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700\"\n            />\n          </div>\n\n          {/* Type Filter */}\n          <select\n            value={filterType}\n            onChange={(e) => setFilterType(e.target.value)}\n            className=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700\"\n          >\n            <option value=\"all\">All Types</option>\n            <option value=\"product\">Product Activities</option>\n            <option value=\"debt\">Debt Activities</option>\n            <option value=\"payment\">Payment Activities</option>\n            <option value=\"login\">Login Activities</option>\n            <option value=\"system\">System Activities</option>\n          </select>\n\n          {/* Date Range */}\n          <select\n            value={dateRange}\n            onChange={(e) => setDateRange(e.target.value)}\n            className=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700\"\n          >\n            <option value=\"7days\">Last 7 days</option>\n            <option value=\"30days\">Last 30 days</option>\n            <option value=\"90days\">Last 90 days</option>\n            <option value=\"all\">All time</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Activity Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-5 gap-4\">\n        {[\n          { type: 'product', label: 'Product Activities', count: historyData.filter(h => h.type === 'product').length },\n          { type: 'debt', label: 'Debt Activities', count: historyData.filter(h => h.type === 'debt').length },\n          { type: 'payment', label: 'Payment Activities', count: historyData.filter(h => h.type === 'payment').length },\n          { type: 'login', label: 'Login Activities', count: historyData.filter(h => h.type === 'login').length },\n          { type: 'system', label: 'System Activities', count: historyData.filter(h => h.type === 'system').length },\n        ].map((stat) => (\n          <div key={stat.type} className=\"card p-4 text-center\">\n            <div className={`inline-flex p-2 rounded-lg mb-2 ${getTypeColor(stat.type)}`}>\n              {getTypeIcon(stat.type)}\n            </div>\n            <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{stat.count}</p>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">{stat.label}</p>\n          </div>\n        ))}\n      </div>\n\n      {/* History List */}\n      <div className=\"card\">\n        <div className=\"p-6 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            Recent Activities ({filteredHistory.length})\n          </h3>\n        </div>\n        \n        <div className=\"divide-y divide-gray-200 dark:divide-gray-700\">\n          {filteredHistory.map((item) => (\n            <div key={item.id} className=\"p-6 hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors\">\n              <div className=\"flex items-start space-x-4\">\n                <div className={`p-2 rounded-lg ${getTypeColor(item.type)}`}>\n                  {getTypeIcon(item.type)}\n                </div>\n                \n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-center justify-between\">\n                    <h4 className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {item.action}\n                    </h4>\n                    <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      {formatTimestamp(item.timestamp)}\n                    </span>\n                  </div>\n                  \n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                    {item.description}\n                  </p>\n                  \n                  <div className=\"flex items-center justify-between mt-2\">\n                    <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      by {item.user}\n                    </span>\n                    \n                    {item.details && (\n                      <button className=\"text-xs text-green-600 dark:text-green-400 hover:underline\">\n                        View Details\n                      </button>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n        \n        {filteredHistory.length === 0 && (\n          <div className=\"p-12 text-center\">\n            <Clock className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n              No activities found\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400\">\n              Try adjusting your search or filter criteria\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAee,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,cAA6B;QACjC;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,aAAa;YACb,MAAM;YACN,WAAW;YACX,SAAS;gBAAE,aAAa;gBAA0B,OAAO;YAAM;QACjE;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,aAAa;YACb,MAAM;YACN,WAAW;YACX,SAAS;gBAAE,UAAU;gBAAkB,QAAQ;YAAM;QACvD;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,aAAa;YACb,MAAM;YACN,WAAW;YACX,SAAS;gBAAE,UAAU;gBAAgB,QAAQ;YAAO;QACtD;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,aAAa;YACb,MAAM;YACN,WAAW;YACX,SAAS;gBAAE,aAAa;gBAAa,UAAU;gBAAI,UAAU;YAAG;QAClE;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,aAAa;YACb,MAAM;YACN,WAAW;YACX,SAAS;gBAAE,WAAW;YAAgB;QACxC;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,aAAa;YACb,MAAM;YACN,WAAW;YACX,SAAS;gBAAE,YAAY;YAAQ;QACjC;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,aAAa;YACb,MAAM;YACN,WAAW;YACX,SAAS;gBAAE,UAAU;gBAAa,WAAW;gBAAO,WAAW;YAAO;QACxE;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,aAAa;YACb,MAAM;YACN,WAAW;YACX,SAAS;gBAAE,aAAa;YAAe;QACzC;KACD;IAED,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,2MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,YAAY,MAAM,CAAC,CAAA;QACzC,MAAM,gBAAgB,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC/D,KAAK,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAC9E,MAAM,cAAc,eAAe,SAAS,KAAK,IAAI,KAAK;QAC1D,OAAO,iBAAiB;IAC1B;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;QAEjF,IAAI,cAAc,GAAG;YACnB,OAAO;QACT,OAAO,IAAI,cAAc,IAAI;YAC3B,OAAO,GAAG,YAAY,UAAU,CAAC;QACnC,OAAO;YACL,OAAO,KAAK,kBAAkB,KAAK,MAAM,KAAK,kBAAkB;QAClE;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,aAAa;YACjB;gBAAC;gBAAa;gBAAQ;gBAAU;gBAAe;aAAO;eACnD,gBAAgB,GAAG,CAAC,CAAA,OAAQ;oBAC7B,KAAK,SAAS;oBACd,KAAK,IAAI;oBACT,KAAK,MAAM;oBACX,KAAK,WAAW;oBAChB,KAAK,IAAI;iBACV;SACF,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC;QAEjC,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAW;QACvD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;QACvC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,uBAAuB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QACnF,EAAE,KAAK;QACP,OAAO,GAAG,CAAC,eAAe,CAAC;IAC7B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmD;;;;;;0CACjE,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;kCAIvD,6LAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMzC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;;;;;;;sCAKd,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAM;;;;;;8CACpB,6LAAC;oCAAO,OAAM;8CAAU;;;;;;8CACxB,6LAAC;oCAAO,OAAM;8CAAO;;;;;;8CACrB,6LAAC;oCAAO,OAAM;8CAAU;;;;;;8CACxB,6LAAC;oCAAO,OAAM;8CAAQ;;;;;;8CACtB,6LAAC;oCAAO,OAAM;8CAAS;;;;;;;;;;;;sCAIzB,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4BAC5C,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAQ;;;;;;8CACtB,6LAAC;oCAAO,OAAM;8CAAS;;;;;;8CACvB,6LAAC;oCAAO,OAAM;8CAAS;;;;;;8CACvB,6LAAC;oCAAO,OAAM;8CAAM;;;;;;;;;;;;;;;;;;;;;;;0BAM1B,6LAAC;gBAAI,WAAU;0BACZ;oBACC;wBAAE,MAAM;wBAAW,OAAO;wBAAsB,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WAAW,MAAM;oBAAC;oBAC5G;wBAAE,MAAM;wBAAQ,OAAO;wBAAmB,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAQ,MAAM;oBAAC;oBACnG;wBAAE,MAAM;wBAAW,OAAO;wBAAsB,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WAAW,MAAM;oBAAC;oBAC5G;wBAAE,MAAM;wBAAS,OAAO;wBAAoB,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM;oBAAC;oBACtG;wBAAE,MAAM;wBAAU,OAAO;wBAAqB,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UAAU,MAAM;oBAAC;iBAC1G,CAAC,GAAG,CAAC,CAAC,qBACL,6LAAC;wBAAoB,WAAU;;0CAC7B,6LAAC;gCAAI,WAAW,CAAC,gCAAgC,EAAE,aAAa,KAAK,IAAI,GAAG;0CACzE,YAAY,KAAK,IAAI;;;;;;0CAExB,6LAAC;gCAAE,WAAU;0CAAoD,KAAK,KAAK;;;;;;0CAC3E,6LAAC;gCAAE,WAAU;0CAA4C,KAAK,KAAK;;;;;;;uBAL3D,KAAK,IAAI;;;;;;;;;;0BAWvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;gCAAsD;gCAC9C,gBAAgB,MAAM;gCAAC;;;;;;;;;;;;kCAI/C,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC;gCAAkB,WAAU;0CAC3B,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC,eAAe,EAAE,aAAa,KAAK,IAAI,GAAG;sDACxD,YAAY,KAAK,IAAI;;;;;;sDAGxB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,KAAK,MAAM;;;;;;sEAEd,6LAAC;4DAAK,WAAU;sEACb,gBAAgB,KAAK,SAAS;;;;;;;;;;;;8DAInC,6LAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;8DAGnB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;gEAA2C;gEACrD,KAAK,IAAI;;;;;;;wDAGd,KAAK,OAAO,kBACX,6LAAC;4DAAO,WAAU;sEAA6D;;;;;;;;;;;;;;;;;;;;;;;;+BA1B/E,KAAK,EAAE;;;;;;;;;;oBAqCpB,gBAAgB,MAAM,KAAK,mBAC1B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAGvE,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;;;;;;;;;;;;;AAQ5D;GAzSwB;KAAA", "debugId": null}}, {"offset": {"line": 6309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/Settings.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Save, User, Store, Bell, Shield, Palette, Database, Download, Upload } from 'lucide-react'\n\nexport default function Settings() {\n  const [activeTab, setActiveTab] = useState('store')\n  const [settings, setSettings] = useState({\n    store: {\n      name: 'Revantad Store',\n      address: '123 Barangay Street, Manila, Philippines',\n      phone: '+63 ************',\n      email: '<EMAIL>',\n      currency: 'PHP',\n      timezone: 'Asia/Manila',\n      businessHours: {\n        open: '06:00',\n        close: '22:00',\n      },\n    },\n    profile: {\n      firstName: 'Admin',\n      lastName: 'User',\n      email: '<EMAIL>',\n      phone: '+63 ************',\n      role: 'Store Owner',\n    },\n    notifications: {\n      lowStock: true,\n      newDebt: true,\n      paymentReceived: true,\n      dailyReport: false,\n      weeklyReport: true,\n      emailNotifications: true,\n      smsNotifications: false,\n    },\n    security: {\n      twoFactorAuth: false,\n      sessionTimeout: '30',\n      passwordExpiry: '90',\n      loginAttempts: '5',\n    },\n    appearance: {\n      theme: 'light',\n      language: 'en',\n      dateFormat: 'MM/DD/YYYY',\n      numberFormat: 'en-US',\n    },\n    backup: {\n      autoBackup: true,\n      backupFrequency: 'daily',\n      retentionDays: '30',\n      lastBackup: '2024-01-20T10:30:00Z',\n    },\n  })\n\n  const tabs = [\n    { id: 'store', label: 'Store Info', icon: Store },\n    { id: 'profile', label: 'Profile', icon: User },\n    { id: 'notifications', label: 'Notifications', icon: Bell },\n    { id: 'security', label: 'Security', icon: Shield },\n    { id: 'appearance', label: 'Appearance', icon: Palette },\n    { id: 'backup', label: 'Backup', icon: Database },\n  ]\n\n  const handleSave = () => {\n    // Save settings logic here\n    console.log('Settings saved:', settings)\n    alert('Settings saved successfully!')\n  }\n\n  const handleExportData = () => {\n    // Export data logic here\n    console.log('Exporting data...')\n    alert('Data export started. You will receive an email when ready.')\n  }\n\n  const handleImportData = () => {\n    // Import data logic here\n    console.log('Importing data...')\n    alert('Please select a backup file to import.')\n  }\n\n  const renderStoreSettings = () => (\n    <div className=\"space-y-6\">\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Store Name\n          </label>\n          <input\n            type=\"text\"\n            value={settings.store.name}\n            onChange={(e) => setSettings({\n              ...settings,\n              store: { ...settings.store, name: e.target.value }\n            })}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700\"\n          />\n        </div>\n        \n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Phone Number\n          </label>\n          <input\n            type=\"tel\"\n            value={settings.store.phone}\n            onChange={(e) => setSettings({\n              ...settings,\n              store: { ...settings.store, phone: e.target.value }\n            })}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700\"\n          />\n        </div>\n      </div>\n      \n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Address\n        </label>\n        <textarea\n          value={settings.store.address}\n          onChange={(e) => setSettings({\n            ...settings,\n            store: { ...settings.store, address: e.target.value }\n          })}\n          rows={3}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700\"\n        />\n      </div>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Opening Time\n          </label>\n          <input\n            type=\"time\"\n            value={settings.store.businessHours.open}\n            onChange={(e) => setSettings({\n              ...settings,\n              store: { \n                ...settings.store, \n                businessHours: { ...settings.store.businessHours, open: e.target.value }\n              }\n            })}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700\"\n          />\n        </div>\n        \n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Closing Time\n          </label>\n          <input\n            type=\"time\"\n            value={settings.store.businessHours.close}\n            onChange={(e) => setSettings({\n              ...settings,\n              store: { \n                ...settings.store, \n                businessHours: { ...settings.store.businessHours, close: e.target.value }\n              }\n            })}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700\"\n          />\n        </div>\n      </div>\n    </div>\n  )\n\n  const renderNotificationSettings = () => (\n    <div className=\"space-y-6\">\n      <div>\n        <h4 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Alert Preferences</h4>\n        <div className=\"space-y-4\">\n          {Object.entries(settings.notifications).map(([key, value]) => (\n            <div key={key} className=\"flex items-center justify-between\">\n              <div>\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}\n                </label>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  {key === 'lowStock' && 'Get notified when products are running low'}\n                  {key === 'newDebt' && 'Alert when new customer debt is recorded'}\n                  {key === 'paymentReceived' && 'Notification for debt payments'}\n                  {key === 'dailyReport' && 'Daily business summary report'}\n                  {key === 'weeklyReport' && 'Weekly business analytics report'}\n                  {key === 'emailNotifications' && 'Receive notifications via email'}\n                  {key === 'smsNotifications' && 'Receive notifications via SMS'}\n                </p>\n              </div>\n              <label className=\"relative inline-flex items-center cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={value as boolean}\n                  onChange={(e) => setSettings({\n                    ...settings,\n                    notifications: { ...settings.notifications, [key]: e.target.checked }\n                  })}\n                  className=\"sr-only peer\"\n                />\n                <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600\"></div>\n              </label>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  )\n\n  const renderBackupSettings = () => (\n    <div className=\"space-y-6\">\n      <div>\n        <h4 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Backup Configuration</h4>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Backup Frequency\n            </label>\n            <select\n              value={settings.backup.backupFrequency}\n              onChange={(e) => setSettings({\n                ...settings,\n                backup: { ...settings.backup, backupFrequency: e.target.value }\n              })}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700\"\n            >\n              <option value=\"daily\">Daily</option>\n              <option value=\"weekly\">Weekly</option>\n              <option value=\"monthly\">Monthly</option>\n            </select>\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Retention Period (Days)\n            </label>\n            <input\n              type=\"number\"\n              value={settings.backup.retentionDays}\n              onChange={(e) => setSettings({\n                ...settings,\n                backup: { ...settings.backup, retentionDays: e.target.value }\n              })}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700\"\n            />\n          </div>\n        </div>\n      </div>\n      \n      <div className=\"border-t border-gray-200 dark:border-gray-700 pt-6\">\n        <h4 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Data Management</h4>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <button\n            onClick={handleExportData}\n            className=\"flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-slate-700\"\n          >\n            <Download className=\"h-5 w-5 mr-2\" />\n            Export All Data\n          </button>\n          \n          <button\n            onClick={handleImportData}\n            className=\"flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-slate-700\"\n          >\n            <Upload className=\"h-5 w-5 mr-2\" />\n            Import Data\n          </button>\n        </div>\n        \n        <div className=\"mt-4 p-4 bg-gray-50 dark:bg-slate-700 rounded-md\">\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n            <strong>Last Backup:</strong> {new Date(settings.backup.lastBackup).toLocaleString()}\n          </p>\n        </div>\n      </div>\n    </div>\n  )\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'store':\n        return renderStoreSettings()\n      case 'notifications':\n        return renderNotificationSettings()\n      case 'backup':\n        return renderBackupSettings()\n      default:\n        return (\n          <div className=\"text-center py-12\">\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} settings coming soon...\n            </p>\n          </div>\n        )\n    }\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Settings</h2>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            Configure your store preferences and system settings\n          </p>\n        </div>\n        <button\n          onClick={handleSave}\n          className=\"btn-primary flex items-center\"\n        >\n          <Save className=\"h-4 w-4 mr-2\" />\n          Save Changes\n        </button>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\n        {/* Settings Navigation */}\n        <div className=\"lg:col-span-1\">\n          <div className=\"card p-4\">\n            <nav className=\"space-y-1\">\n              {tabs.map((tab) => {\n                const Icon = tab.icon\n                return (\n                  <button\n                    key={tab.id}\n                    onClick={() => setActiveTab(tab.id)}\n                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${\n                      activeTab === tab.id\n                        ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'\n                        : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-slate-700'\n                    }`}\n                  >\n                    <Icon className=\"h-4 w-4 mr-3\" />\n                    {tab.label}\n                  </button>\n                )\n              })}\n            </nav>\n          </div>\n        </div>\n\n        {/* Settings Content */}\n        <div className=\"lg:col-span-3\">\n          <div className=\"card p-6\">\n            {renderTabContent()}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;YACL,MAAM;YACN,SAAS;YACT,OAAO;YACP,OAAO;YACP,UAAU;YACV,UAAU;YACV,eAAe;gBACb,MAAM;gBACN,OAAO;YACT;QACF;QACA,SAAS;YACP,WAAW;YACX,UAAU;YACV,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA,eAAe;YACb,UAAU;YACV,SAAS;YACT,iBAAiB;YACjB,aAAa;YACb,cAAc;YACd,oBAAoB;YACpB,kBAAkB;QACpB;QACA,UAAU;YACR,eAAe;YACf,gBAAgB;YAChB,gBAAgB;YAChB,eAAe;QACjB;QACA,YAAY;YACV,OAAO;YACP,UAAU;YACV,YAAY;YACZ,cAAc;QAChB;QACA,QAAQ;YACN,YAAY;YACZ,iBAAiB;YACjB,eAAe;YACf,YAAY;QACd;IACF;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAS,OAAO;YAAc,MAAM,uMAAA,CAAA,QAAK;QAAC;QAChD;YAAE,IAAI;YAAW,OAAO;YAAW,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC9C;YAAE,IAAI;YAAiB,OAAO;YAAiB,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC1D;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,yMAAA,CAAA,SAAM;QAAC;QAClD;YAAE,IAAI;YAAc,OAAO;YAAc,MAAM,2MAAA,CAAA,UAAO;QAAC;QACvD;YAAE,IAAI;YAAU,OAAO;YAAU,MAAM,6MAAA,CAAA,WAAQ;QAAC;KACjD;IAED,MAAM,aAAa;QACjB,2BAA2B;QAC3B,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,MAAM;IACR;IAEA,MAAM,mBAAmB;QACvB,yBAAyB;QACzB,QAAQ,GAAG,CAAC;QACZ,MAAM;IACR;IAEA,MAAM,mBAAmB;QACvB,yBAAyB;QACzB,QAAQ,GAAG,CAAC;QACZ,MAAM;IACR;IAEA,MAAM,sBAAsB,kBAC1B,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,MAAK;oCACL,OAAO,SAAS,KAAK,CAAC,IAAI;oCAC1B,UAAU,CAAC,IAAM,YAAY;4CAC3B,GAAG,QAAQ;4CACX,OAAO;gDAAE,GAAG,SAAS,KAAK;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC;wCACnD;oCACA,WAAU;;;;;;;;;;;;sCAId,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,MAAK;oCACL,OAAO,SAAS,KAAK,CAAC,KAAK;oCAC3B,UAAU,CAAC,IAAM,YAAY;4CAC3B,GAAG,QAAQ;4CACX,OAAO;gDAAE,GAAG,SAAS,KAAK;gDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4CAAC;wCACpD;oCACA,WAAU;;;;;;;;;;;;;;;;;;8BAKhB,6LAAC;;sCACC,6LAAC;4BAAM,WAAU;sCAAkE;;;;;;sCAGnF,6LAAC;4BACC,OAAO,SAAS,KAAK,CAAC,OAAO;4BAC7B,UAAU,CAAC,IAAM,YAAY;oCAC3B,GAAG,QAAQ;oCACX,OAAO;wCAAE,GAAG,SAAS,KAAK;wCAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oCAAC;gCACtD;4BACA,MAAM;4BACN,WAAU;;;;;;;;;;;;8BAId,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,MAAK;oCACL,OAAO,SAAS,KAAK,CAAC,aAAa,CAAC,IAAI;oCACxC,UAAU,CAAC,IAAM,YAAY;4CAC3B,GAAG,QAAQ;4CACX,OAAO;gDACL,GAAG,SAAS,KAAK;gDACjB,eAAe;oDAAE,GAAG,SAAS,KAAK,CAAC,aAAa;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACzE;wCACF;oCACA,WAAU;;;;;;;;;;;;sCAId,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,MAAK;oCACL,OAAO,SAAS,KAAK,CAAC,aAAa,CAAC,KAAK;oCACzC,UAAU,CAAC,IAAM,YAAY;4CAC3B,GAAG,QAAQ;4CACX,OAAO;gDACL,GAAG,SAAS,KAAK;gDACjB,eAAe;oDAAE,GAAG,SAAS,KAAK,CAAC,aAAa;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC1E;wCACF;oCACA,WAAU;;;;;;;;;;;;;;;;;;;;;;;;IAOpB,MAAM,6BAA6B,kBACjC,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;kCACvE,6LAAC;wBAAI,WAAU;kCACZ,OAAO,OAAO,CAAC,SAAS,aAAa,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACvD,6LAAC;gCAAc,WAAU;;kDACvB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DACd,IAAI,OAAO,CAAC,YAAY,OAAO,OAAO,CAAC,MAAM,CAAA,MAAO,IAAI,WAAW;;;;;;0DAEtE,6LAAC;gDAAE,WAAU;;oDACV,QAAQ,cAAc;oDACtB,QAAQ,aAAa;oDACrB,QAAQ,qBAAqB;oDAC7B,QAAQ,iBAAiB;oDACzB,QAAQ,kBAAkB;oDAC1B,QAAQ,wBAAwB;oDAChC,QAAQ,sBAAsB;;;;;;;;;;;;;kDAGnC,6LAAC;wCAAM,WAAU;;0DACf,6LAAC;gDACC,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,IAAM,YAAY;wDAC3B,GAAG,QAAQ;wDACX,eAAe;4DAAE,GAAG,SAAS,aAAa;4DAAE,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,OAAO;wDAAC;oDACtE;gDACA,WAAU;;;;;;0DAEZ,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;+BAzBT;;;;;;;;;;;;;;;;;;;;;IAkCpB,MAAM,uBAAuB,kBAC3B,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAyD;;;;;;sCACvE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,SAAS,MAAM,CAAC,eAAe;4CACtC,UAAU,CAAC,IAAM,YAAY;oDAC3B,GAAG,QAAQ;oDACX,QAAQ;wDAAE,GAAG,SAAS,MAAM;wDAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAChE;4CACA,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;;;;;;;;;;;;;8CAI5B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,MAAM,CAAC,aAAa;4CACpC,UAAU,CAAC,IAAM,YAAY;oDAC3B,GAAG,QAAQ;oDACX,QAAQ;wDAAE,GAAG,SAAS,MAAM;wDAAE,eAAe,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC9D;4CACA,WAAU;;;;;;;;;;;;;;;;;;;;;;;;8BAMlB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyD;;;;;;sCACvE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAIvC,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAKvC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;kDACX,6LAAC;kDAAO;;;;;;oCAAqB;oCAAE,IAAI,KAAK,SAAS,MAAM,CAAC,UAAU,EAAE,cAAc;;;;;;;;;;;;;;;;;;;;;;;;IAO5F,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,qBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BACV,UAAU,MAAM,CAAC,GAAG,WAAW,KAAK,UAAU,KAAK,CAAC;4BAAG;;;;;;;;;;;;QAIlE;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmD;;;;;;0CACjE,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;kCAIvD,6LAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAKrC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,KAAK,GAAG,CAAC,CAAC;oCACT,MAAM,OAAO,IAAI,IAAI;oCACrB,qBACE,6LAAC;wCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;wCAClC,WAAW,CAAC,oFAAoF,EAC9F,cAAc,IAAI,EAAE,GAChB,yEACA,6EACJ;;0DAEF,6LAAC;gDAAK,WAAU;;;;;;4CACf,IAAI,KAAK;;uCATL,IAAI,EAAE;;;;;gCAYjB;;;;;;;;;;;;;;;;kCAMN,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GA7VwB;KAAA", "debugId": null}}, {"offset": {"line": 7101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg'\n  color?: 'primary' | 'secondary' | 'white'\n  text?: string\n}\n\nexport default function LoadingSpinner({ \n  size = 'md', \n  color = 'primary', \n  text \n}: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  }\n\n  const colorClasses = {\n    primary: 'border-green-500 border-t-transparent',\n    secondary: 'border-yellow-400 border-t-transparent',\n    white: 'border-white border-t-transparent'\n  }\n\n  return (\n    <div className=\"flex flex-col items-center justify-center space-y-3\">\n      <motion.div\n        className={`${sizeClasses[size]} border-2 ${colorClasses[color]} rounded-full`}\n        animate={{ rotate: 360 }}\n        transition={{\n          duration: 1,\n          repeat: Infinity,\n          ease: \"linear\"\n        }}\n      />\n      {text && (\n        <motion.p\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.2 }}\n          className=\"text-sm text-gray-600 dark:text-gray-400\"\n        >\n          {text}\n        </motion.p>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUe,SAAS,eAAe,EACrC,OAAO,IAAI,EACX,QAAQ,SAAS,EACjB,IAAI,EACgB;IACpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe;QACnB,SAAS;QACT,WAAW;QACX,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,UAAU,EAAE,YAAY,CAAC,MAAM,CAAC,aAAa,CAAC;gBAC9E,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;YAED,sBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gBACP,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;0BAET;;;;;;;;;;;;AAKX;KAxCwB", "debugId": null}}, {"offset": {"line": 7174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/ProtectedRoute.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n}\n\nexport default function ProtectedRoute({ children }: ProtectedRouteProps) {\n  const { isAuthenticated, isLoading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!isLoading && !isAuthenticated) {\n      router.push('/login')\n    }\n  }, [isAuthenticated, isLoading, router])\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-slate-900\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 hero-gradient rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse\">\n            <span className=\"text-white font-bold text-2xl\">R</span>\n          </div>\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n            Loading Revantad Store\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Please wait while we prepare your dashboard...\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-slate-900\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <span className=\"text-red-600 dark:text-red-400 font-bold text-2xl\">!</span>\n          </div>\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n            Access Denied\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Redirecting to login page...\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUe,SAAS,eAAe,EAAE,QAAQ,EAAuB;;IACtE,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,aAAa,CAAC,iBAAiB;gBAClC,OAAO,IAAI,CAAC;YACd;QACF;mCAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAgC;;;;;;;;;;;kCAElD,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAMxD;IAEA,IAAI,CAAC,iBAAiB;QACpB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAoD;;;;;;;;;;;kCAEtE,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAMxD;IAEA,qBAAO;kBAAG;;AACZ;GA/CwB;;QACiB,kIAAA,CAAA,UAAO;QAC/B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 7322, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/index.ts"], "sourcesContent": ["// Component exports for better organization and cleaner imports\n// This file serves as a central export point for all components\n\n// Layout Components\nexport { default as AdminHeader } from './AdminHeader'\nexport { default as Sidebar } from './Sidebar'\n\n// Dashboard Components\nexport { default as DashboardStats } from './DashboardStats'\nexport { default as APIGraphing } from './APIGraphing'\n\n// Product Management Components\nexport { default as ProductsSection } from './ProductsSection'\nexport { default as ProductModal } from './ProductModal'\n\n// Debt Management Components\nexport { default as DebtsSection } from './DebtsSection'\nexport { default as DebtModal } from './DebtModal'\n\n// Feature Components\nexport { default as FamilyGallery } from './FamilyGallery'\nexport { default as Calendar } from './Calendar'\nexport { default as History } from './History'\nexport { default as Settings } from './Settings'\n\n// Utility Components\nexport { default as LoadingSpinner } from './LoadingSpinner'\nexport { default as ProtectedRoute } from './ProtectedRoute'\nexport { ThemeProvider } from './ThemeProvider'\n\n// Re-export commonly used types\nexport type { Product, CustomerDebt } from '@/lib/supabase'\n"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,gEAAgE;AAEhE,oBAAoB;;AACpB;AACA;AAEA,uBAAuB;AACvB;AACA;AAEA,gCAAgC;AAChC;AACA;AAEA,6BAA6B;AAC7B;AACA;AAEA,qBAAqB;AACrB;AACA;AACA;AACA;AAEA,qBAAqB;AACrB;AACA;AACA", "debugId": null}}, {"offset": {"line": 7503, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/app/admin/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n\nimport {\n  AdminHeader,\n  Sidebar,\n  ProductsSection,\n  DebtsSection,\n  DashboardStats,\n  FamilyGallery,\n  APIGraphing,\n  History,\n  Calendar,\n  Settings,\n  ProtectedRoute\n} from '@/components'\nimport type { DashboardStats as DashboardStatsType } from '@/types'\n\nexport default function AdminPage() {\n  const [activeSection, setActiveSection] = useState('dashboard')\n  const { resolvedTheme } = useTheme()\n  const [stats, setStats] = useState<DashboardStatsType>({\n    totalProducts: 0,\n    totalDebts: 0,\n    totalDebtAmount: 0,\n    lowStockItems: 0,\n    recentProducts: [],\n    recentDebts: []\n  })\n\n  useEffect(() => {\n    fetchStats()\n  }, [])\n\n  const fetchStats = async () => {\n    try {\n      // Fetch products\n      const productsRes = await fetch('/api/products')\n      const productsData = await productsRes.json()\n      const products = productsData.products || []\n\n      // Fetch debts\n      const debtsRes = await fetch('/api/debts')\n      const debtsData = await debtsRes.json()\n      const debts = debtsData.debts || []\n\n      // Calculate stats\n      const totalDebtAmount = debts.reduce((sum: number, debt: { total_amount: number }) => sum + debt.total_amount, 0)\n      const lowStockProducts = products.filter((product: { stock_quantity: number }) => product.stock_quantity < 10).length\n\n      setStats({\n        totalProducts: products.length,\n        totalDebts: debts.length,\n        totalDebtAmount,\n        lowStockItems: lowStockProducts,\n        recentProducts: products.slice(0, 5),\n        recentDebts: debts.slice(0, 5)\n      })\n    } catch (error) {\n      console.error('Error fetching stats:', error)\n    }\n  }\n\n  const renderContent = () => {\n    switch (activeSection) {\n      case 'products':\n        return <ProductsSection onStatsUpdate={fetchStats} />\n      case 'debts':\n        return <DebtsSection onStatsUpdate={fetchStats} />\n      case 'family-gallery':\n        return <FamilyGallery />\n      case 'api-graphing':\n        return <APIGraphing stats={stats} />\n      case 'history':\n        return <History />\n      case 'calendar':\n        return <Calendar />\n      case 'settings':\n        return <Settings />\n      default:\n        return <DashboardStats stats={stats} />\n    }\n  }\n\n  const getPageTitle = () => {\n    switch (activeSection) {\n      case 'dashboard':\n        return 'Dashboard'\n      case 'products':\n        return 'Product Lists'\n      case 'debts':\n        return 'Customer Debt Management'\n      case 'family-gallery':\n        return 'Family Gallery'\n      case 'api-graphing':\n        return 'API Graphing & Visuals'\n      case 'history':\n        return 'History'\n      case 'calendar':\n        return 'Calendar'\n      case 'settings':\n        return 'Settings'\n      default:\n        return 'Dashboard'\n    }\n  }\n\n  const getPageDescription = () => {\n    switch (activeSection) {\n      case 'dashboard':\n        return 'Overview of your Revantad Store'\n      case 'products':\n        return 'Manage your product lists with CRUD operations'\n      case 'debts':\n        return 'Track customer debt and payments'\n      case 'family-gallery':\n        return 'Manage family photos and memories'\n      case 'api-graphing':\n        return 'Visual analytics and business insights'\n      case 'history':\n        return 'View transaction and activity history'\n      case 'calendar':\n        return 'Manage events and schedules'\n      case 'settings':\n        return 'Configure your store settings'\n      default:\n        return 'Overview of your Revantad Store'\n    }\n  }\n\n  return (\n    <ProtectedRoute>\n      <div\n        className=\"min-h-screen bg-gray-50 dark:bg-slate-900 transition-colors duration-300\"\n        style={{\n          backgroundColor: resolvedTheme === 'dark' ? '#0f172a' : '#f9fafb'\n        }}\n      >\n        {/* Facebook-style Header */}\n        <AdminHeader\n          activeSection={activeSection}\n          setActiveSection={setActiveSection}\n        />\n\n        <div className=\"flex pt-16 h-screen\">\n          {/* Updated Sidebar */}\n          <Sidebar activeSection={activeSection} setActiveSection={setActiveSection} />\n\n          {/* Main Content - Fixed height with proper scrollbar constraint */}\n          <main\n            className=\"flex-1 transition-colors duration-300 h-[calc(100vh-4rem)] overflow-hidden relative\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' ? '#0f172a' : '#ffffff'\n            }}\n          >\n            <div className=\"h-full overflow-y-auto main-content-scroll\">\n              <div className=\"p-4 sm:p-6 lg:p-8\">\n                <div className=\"mb-6 sm:mb-8\">\n                  <h1\n                    className=\"text-2xl sm:text-3xl font-bold transition-colors duration-300\"\n                    style={{\n                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#1f2937'\n                    }}\n                  >\n                    {getPageTitle()}\n                  </h1>\n                  <p\n                    className=\"mt-2 text-sm sm:text-base transition-colors duration-300\"\n                    style={{\n                      color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                    }}\n                  >\n                    {getPageDescription()}\n                  </p>\n                </div>\n                {renderContent()}\n              </div>\n            </div>\n\n            {/* Subtle scroll fade indicators for better UX */}\n            <div className=\"absolute top-0 left-0 right-0 h-4 pointer-events-none z-10 bg-gradient-to-b from-current to-transparent opacity-5\"></div>\n            <div className=\"absolute bottom-0 left-0 right-0 h-4 pointer-events-none z-10 bg-gradient-to-t from-current to-transparent opacity-5\"></div>\n          </main>\n        </div>\n      </div>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;AAoBe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;QACrD,eAAe;QACf,YAAY;QACZ,iBAAiB;QACjB,eAAe;QACf,gBAAgB,EAAE;QAClB,aAAa,EAAE;IACjB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,iBAAiB;YACjB,MAAM,cAAc,MAAM,MAAM;YAChC,MAAM,eAAe,MAAM,YAAY,IAAI;YAC3C,MAAM,WAAW,aAAa,QAAQ,IAAI,EAAE;YAE5C,cAAc;YACd,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,QAAQ,UAAU,KAAK,IAAI,EAAE;YAEnC,kBAAkB;YAClB,MAAM,kBAAkB,MAAM,MAAM,CAAC,CAAC,KAAa,OAAmC,MAAM,KAAK,YAAY,EAAE;YAC/G,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC,UAAwC,QAAQ,cAAc,GAAG,IAAI,MAAM;YAErH,SAAS;gBACP,eAAe,SAAS,MAAM;gBAC9B,YAAY,MAAM,MAAM;gBACxB;gBACA,eAAe;gBACf,gBAAgB,SAAS,KAAK,CAAC,GAAG;gBAClC,aAAa,MAAM,KAAK,CAAC,GAAG;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,sLAAA,CAAA,kBAAe;oBAAC,eAAe;;;;;;YACzC,KAAK;gBACH,qBAAO,6LAAC,gLAAA,CAAA,eAAY;oBAAC,eAAe;;;;;;YACtC,KAAK;gBACH,qBAAO,6LAAC,kLAAA,CAAA,gBAAa;;;;;YACvB,KAAK;gBACH,qBAAO,6LAAC,8KAAA,CAAA,cAAW;oBAAC,OAAO;;;;;;YAC7B,KAAK;gBACH,qBAAO,6LAAC,sKAAA,CAAA,UAAO;;;;;YACjB,KAAK;gBACH,qBAAO,6LAAC,wKAAA,CAAA,WAAQ;;;;;YAClB,KAAK;gBACH,qBAAO,6LAAC,wKAAA,CAAA,WAAQ;;;;;YAClB;gBACE,qBAAO,6LAAC,oLAAA,CAAA,iBAAc;oBAAC,OAAO;;;;;;QAClC;IACF;IAEA,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,oLAAA,CAAA,iBAAc;kBACb,cAAA,6LAAC;YACC,WAAU;YACV,OAAO;gBACL,iBAAiB,kBAAkB,SAAS,YAAY;YAC1D;;8BAGA,6LAAC,8KAAA,CAAA,cAAW;oBACV,eAAe;oBACf,kBAAkB;;;;;;8BAGpB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,sKAAA,CAAA,UAAO;4BAAC,eAAe;4BAAe,kBAAkB;;;;;;sCAGzD,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,iBAAiB,kBAAkB,SAAS,YAAY;4BAC1D;;8CAEA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAU;wDACV,OAAO;4DACL,OAAO,kBAAkB,SAAS,YAAY;wDAChD;kEAEC;;;;;;kEAEH,6LAAC;wDACC,WAAU;wDACV,OAAO;4DACL,OAAO,kBAAkB,SAAS,YAAY;wDAChD;kEAEC;;;;;;;;;;;;4CAGJ;;;;;;;;;;;;8CAKL,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;GAzKwB;;QAEI,mJAAA,CAAA,WAAQ;;;KAFZ", "debugId": null}}]}